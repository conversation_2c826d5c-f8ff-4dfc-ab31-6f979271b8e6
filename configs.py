import os

from dotenv import load_dotenv

config_info = {
    'market': '000300',
    'rep_market_id': None,
    'assist_model_name': '',
    'model_name': '',
    'topn': 10,
    'max_sell': 2,
    'select_range': 5,
    'start_date': '20050101',
    'assist_feature_count': 397,
    'feature_count': 397,
    'period_n': 10,
    'assist_period_n': 10,
    'alpha_type': 'a458',
    'assist_alpha_type': 'a458',
    'presets': 'medium_quality',
    'tuning_days': 450,
    'more_weights_days' : 450,
    'train_delay': 0,
    'interval': 250,
    'interest': [],
    'train_type': 'reg',
    'assist_train_type': 'reg',
    'max_return': {1: 0.04, 5: 0.08, 10: 0.10, 20: 0.12},
    'max_weight': 1,
    'curve': 1.1,
    'curve_2': 1.1,
    'min_return': 0.02,
    'secid_filter': [],
    'secid_filter_train': [],
    
    'models_to_use': ['GBM', 'CAT', 'FASTAI', "XGB"],  # 可选: 'GBM', 'XGB', 'CAT', 'FASTAI', 'NN_TORCH', 'RF', 'XT'
    'gbm_to_use': [1,2,0],  # 是否使用GBM的第三个配置

    'update_data': True,  # True则生成特征
    'download_data': False,  # True则下载数据
    'recommend_mode': True,  # True则生成推荐结果
    'generate_all_features': False,  # True生成全部特征，False只生成模型中使用的特征
    'notify_by_mail': False,
    'generate_ins_date': None,
    'auto_feature_select': False,
    'hyperparameter_tune_kwargs': False,
    'cpu': True,
    'pass': 0,
    'mail': '<EMAIL>',
    'train_year': 6,
    'weights421': True,
    'wencai_scores_path': None,  # 'score-fit.csv',
    'fixed_model': None,
    'assist_fixed_model': None,
    'index_enhance': True,
    'add_info': False,
    'train_with_add_features': False,
    'topn_dropout_k': 8.0,
    'bottom_rank':100,
    'added_alpha_for_stratergy': None,
    'skip_days' : 0,
    'trade_log': False,
    'selected_year': [],
    'max_down': 1.0,
    'day_max_down' : 1.0,
    'consider_vix': True,
    'buy_high_price': True,
    'filter_negative_pe': False,  # True则过滤掉PE为负的股票
    'filter_profit_growth': False,  # True则过滤掉业绩不增长的股票
    'vix_down_level': 0.05,
    'vix_up_level': 0.02,
    'qlib_url': os.path.expanduser('~/qlib_bin_today'),
    'consider_trade_date': False,
    'enable_min_res': True,
    'dump_change': True,
    'ic_year': None,
    'exp_features':False,
    'prepared_data_lens': 800,
    'index_list_before_year': 3,
    'alpha_class': 'alpha_101.Alphas101,alpha_191.Alphas191',
    'alpha_158': True,
    'index_feature_count': 0,
    'train_time_limit': 0,
    'random_backtest': False,
    'feature_for_model': None,
    'ag_verbosity':2,
    'hold_till_end': 0,
    'wencai_prompt': '总市值小于20亿的股票',
    'train_seed': 0,
    'num_stack_levels': 0,
    'num_bag_folds': 8,
    'zeroshot': True,
    'max_time_sub_model': 450,
    'enable_second_ensemble': False,
    'sub_model_name': None,
    'remove_from_fid': 250,
    'serial_stratergy': 'SerializedTopNStratergy',
    'use_rank_model': False,
    'decorrelated_top_features': False,
}

index_map = {
    "csi300": "000300",
    "csi500": "000905",
    "csi800": "000906",
    "csi1000": "000852",
    "csiall": "000985",

    "csilow": "900002",
    "csimini": "900001",
    "csi2000": "399303",
    "csi200": "000904",
    "csi100": "000903",
    "csil300": "399012",
}
hyperparameter_portfolio_small = {
    "REALMLP": {},
    "TABM":{
        "ag_args": {"name_suffix": "_500k_mini", "priority": -7},
        "arch_type": "tabm-mini",
        "amp": True,
        "batch_size": "auto",         # ⇒ 1024
        "d_block": 512,
        "d_embedding": 24,
        "n_blocks": 3,
        "tabm_k": 32,
        "dropout": 0.2,
        "num_emb_type": "pwl",
        "num_emb_n_bins": 32,
        "lr": 0.0012,
        "weight_decay": 0.0001,
        "gradient_clipping_norm": 1.0,
        "patience": 24,
        "share_training_batches": True
    },

    "XGB": [
        { 'seed': 0}
    ],
    "CAT": [
        {
            'random_seed': 0,
            "depth": 8,
            "grow_policy": "Depthwise",
            "l2_leaf_reg": 2.7997999596449104,
            "learning_rate": 0.031375015734637225,
            "max_ctr_complexity": 2,
            "one_hot_max_size": 3,
            "ag_args": {"name_suffix": "_r9", "priority": -5},
        },
        {
            'random_seed': 0,
            "depth": 8,
            "grow_policy": "SymmetricTree",
            "l2_leaf_reg": 3.3274013177541373,
            "learning_rate": 0.017301189655111057,
            "max_ctr_complexity": 5,
            "one_hot_max_size": 10,
            "ag_args": {"name_suffix": "_r13", "priority": -12},
        },
    ],
    "GBM": [
        {
            'seed': 0,
            'bagging_seed': 0,
            'feature_fraction_seed': 0,
            'data_random_seed': 0,
            "extra_trees": True,
            "feature_fraction": 0.5143401489640409,
            "learning_rate": 0.00529479887023554,
            "min_data_in_leaf": 6,
            "num_leaves": 133,
            "ag_args": {"name_suffix": "_r196", "priority": -31},
        },
        {
            'seed': 0,
            'bagging_seed': 0,
            'feature_fraction_seed': 0,
            'data_random_seed': 0,
            "extra_trees": True,
            "feature_fraction": 0.5730390983988963,
            "learning_rate": 0.010305352949119608,
            "min_data_in_leaf": 10,
            "num_leaves": 215,
            "ag_args": {"name_suffix": "_r121", "priority": -74},
        }
    ],
    "FASTAI": [
        {
            'seed': 0,
            "bs": 256,
            "emb_drop": 0.5411770367537934,
            "epochs": 43,
            "layers": [800, 400],
            "lr": 0.01519848858318159,
            "ps": 0.23782946566604385,
            "ag_args": {"name_suffix": "_r191", "priority": -4},
        },
    ],
}


hyperparameter_filterd_medium = {
    "NN_TORCH": [
        {},
        {
            "activation": "elu",
            "dropout_prob": 0.24622382571353768,
            "hidden_size": 159,
            "learning_rate": 0.008507536855608535,
            "num_layers": 5,
            "use_batchnorm": True,
            "weight_decay": 1.8201539594953562e-06,
            "ag_args": {"name_suffix": "_r30", "priority": -17},
        },
        {
            "activation": "relu",
            "dropout_prob": 0.09976801642258049,
            "hidden_size": 135,
            "learning_rate": 0.001631450730978947,
            "num_layers": 5,
            "use_batchnorm": False,
            "weight_decay": 3.867683394425807e-05,
            "ag_args": {"name_suffix": "_r86", "priority": -19},
        },
        {
            "activation": "elu",
            "dropout_prob": 0.01030258381183309,
            "hidden_size": 111,
            "learning_rate": 0.01845979186513771,
            "num_layers": 5,
            "use_batchnorm": True,
            "weight_decay": 0.00020238017476912164,
            "ag_args": {"name_suffix": "_r158", "priority": -38},
        },
        {
            "activation": "elu",
            "dropout_prob": 0.1703783780377607,
            "hidden_size": 212,
            "learning_rate": 0.0004107199833213839,
            "num_layers": 5,
            "use_batchnorm": True,
            "weight_decay": 1.105439140660822e-07,
            "ag_args": {"name_suffix": "_r143", "priority": -49},
        },
        {
            "activation": "elu",
            "dropout_prob": 0.013288954106470907,
            "hidden_size": 81,
            "learning_rate": 0.005340914647396154,
            "num_layers": 4,
            "use_batchnorm": False,
            "weight_decay": 8.762168370775353e-05,
            "ag_args": {"name_suffix": "_r31", "priority": -52},
        },
        {
            "activation": "relu",
            "dropout_prob": 0.36669080773207274,
            "hidden_size": 95,
            "learning_rate": 0.015280159186761077,
            "num_layers": 3,
            "use_batchnorm": True,
            "weight_decay": 1.3082489374636015e-08,
            "ag_args": {"name_suffix": "_r87", "priority": -59},
        },
        {
            "activation": "relu",
            "dropout_prob": 0.12166942295569863,
            "hidden_size": 151,
            "learning_rate": 0.0018866871631794007,
            "num_layers": 4,
            "use_batchnorm": True,
            "weight_decay": 9.190843763153802e-05,
            "ag_args": {"name_suffix": "_r185", "priority": -65},
        },
        {
            "activation": "relu",
            "dropout_prob": 0.33926015213879396,
            "hidden_size": 247,
            "learning_rate": 0.0029983839090226075,
            "num_layers": 5,
            "use_batchnorm": False,
            "weight_decay": 0.00038926240517691234,
            "ag_args": {"name_suffix": "_r121", "priority": -79},
        },
        {
            "activation": "relu",
            "dropout_prob": 0.23713784729000734,
            "hidden_size": 200,
            "learning_rate": 0.00311256170909018,
            "num_layers": 4,
            "use_batchnorm": True,
            "weight_decay": 4.573016756474468e-08,
            "ag_args": {"name_suffix": "_r1", "priority": -96},
        },
    ],
    "GBM": [
        {},
        {"extra_trees": True, "ag_args": {"name_suffix": "XT"}},
        {"learning_rate": 0.03, "num_leaves": 128, "feature_fraction": 0.9, "min_data_in_leaf": 3, "ag_args": {"name_suffix": "Large", "priority": 0, "hyperparameter_tune_kwargs": None}},
        {"extra_trees": False, "feature_fraction": 0.7023601671276614, "learning_rate": 0.012144796373999013, "min_data_in_leaf": 14, "num_leaves": 53, "ag_args": {"name_suffix": "_r131", "priority": -3}},
        {"extra_trees": True, "feature_fraction": 0.5636931414546802, "learning_rate": 0.01518660230385841, "min_data_in_leaf": 48, "num_leaves": 16, "ag_args": {"name_suffix": "_r96", "priority": -6}},
        {"extra_trees": True, "feature_fraction": 0.8282601210460099, "learning_rate": 0.033929021353492905, "min_data_in_leaf": 6, "num_leaves": 127, "ag_args": {"name_suffix": "_r188", "priority": -14}},
        {"extra_trees": False, "feature_fraction": 0.6245777099925497, "learning_rate": 0.04711573688184715, "min_data_in_leaf": 56, "num_leaves": 89, "ag_args": {"name_suffix": "_r130", "priority": -18}},
        {"extra_trees": False, "feature_fraction": 0.5898927512279213, "learning_rate": 0.010464516487486093, "min_data_in_leaf": 11, "num_leaves": 252, "ag_args": {"name_suffix": "_r161", "priority": -27}},
        {"extra_trees": True, "feature_fraction": 0.5143401489640409, "learning_rate": 0.00529479887023554, "min_data_in_leaf": 6, "num_leaves": 133, "ag_args": {"name_suffix": "_r196", "priority": -31}},
        {"extra_trees": False, "feature_fraction": 0.7421180622507277, "learning_rate": 0.018603888565740096, "min_data_in_leaf": 6, "num_leaves": 22, "ag_args": {"name_suffix": "_r15", "priority": -37}},
        {"extra_trees": False, "feature_fraction": 0.9408897917880529, "learning_rate": 0.01343464462043561, "min_data_in_leaf": 21, "num_leaves": 178, "ag_args": {"name_suffix": "_r143", "priority": -44}},
        {"extra_trees": True, "feature_fraction": 0.4341088458599442, "learning_rate": 0.04034449862560467, "min_data_in_leaf": 33, "num_leaves": 16, "ag_args": {"name_suffix": "_r94", "priority": -48}},
        {"extra_trees": True, "feature_fraction": 0.9773131270704629, "learning_rate": 0.010534290864227067, "min_data_in_leaf": 21, "num_leaves": 111, "ag_args": {"name_suffix": "_r30", "priority": -56}},
        {"extra_trees": False, "feature_fraction": 0.8254432681390782, "learning_rate": 0.031251656439648626, "min_data_in_leaf": 50, "num_leaves": 210, "ag_args": {"name_suffix": "_r135", "priority": -69}},
        {"extra_trees": False, "feature_fraction": 0.5730390983988963, "learning_rate": 0.010305352949119608, "min_data_in_leaf": 10, "num_leaves": 215, "ag_args": {"name_suffix": "_r121", "priority": -74}},
    ],
    "CAT": [
        {'ag_args_fit':{'num_gpus':0}},
        {"depth": 6, "grow_policy": "SymmetricTree", "l2_leaf_reg": 2.1542798306067823, "learning_rate": 0.06864209415792857, "max_ctr_complexity": 4, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r177", "priority": -1}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 8, "grow_policy": "Depthwise", "l2_leaf_reg": 2.7997999596449104, "learning_rate": 0.031375015734637225, "max_ctr_complexity": 2, "one_hot_max_size": 3, "ag_args": {"name_suffix": "_r9", "priority": -5}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 4, "grow_policy": "SymmetricTree", "l2_leaf_reg": 4.559174625782161, "learning_rate": 0.04939557741379516, "max_ctr_complexity": 3, "one_hot_max_size": 3, "ag_args": {"name_suffix": "_r137", "priority": -10}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 8, "grow_policy": "SymmetricTree", "l2_leaf_reg": 3.3274013177541373, "learning_rate": 0.017301189655111057, "max_ctr_complexity": 5, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r13", "priority": -12}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 5, "grow_policy": "SymmetricTree", "l2_leaf_reg": 1.0457098345001241, "learning_rate": 0.050294288910022224, "max_ctr_complexity": 5, "one_hot_max_size": 2, "ag_args": {"name_suffix": "_r69", "priority": -24}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 6, "grow_policy": "Depthwise", "l2_leaf_reg": 1.3584121369544215, "learning_rate": 0.03743901034980473, "max_ctr_complexity": 3, "one_hot_max_size": 2, "ag_args": {"name_suffix": "_r70", "priority": -29}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 8, "grow_policy": "Depthwise", "l2_leaf_reg": 1.6376578537958237, "learning_rate": 0.032899230324940465, "max_ctr_complexity": 3, "one_hot_max_size": 2, "ag_args": {"name_suffix": "_r86", "priority": -39}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 4, "grow_policy": "SymmetricTree", "l2_leaf_reg": 3.353268454214423, "learning_rate": 0.06028218319511302, "max_ctr_complexity": 1, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r49", "priority": -42}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 8, "grow_policy": "Depthwise", "l2_leaf_reg": 1.640921865280573, "learning_rate": 0.03623295190021361, "max_ctr_complexity": 3, "one_hot_max_size": 5, "ag_args": {"name_suffix": "_r128", "priority": -50}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 4, "grow_policy": "SymmetricTree", "l2_leaf_reg": 2.894432181094842, "learning_rate": 0.055078095725390575, "max_ctr_complexity": 4, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r5", "priority": -58}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 7, "grow_policy": "SymmetricTree", "l2_leaf_reg": 1.6761016245166451, "learning_rate": 0.06566144806528762, "max_ctr_complexity": 2, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r143", "priority": -61}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 5, "grow_policy": "SymmetricTree", "l2_leaf_reg": 3.3217885487525205, "learning_rate": 0.05291587380674719, "max_ctr_complexity": 5, "one_hot_max_size": 3, "ag_args": {"name_suffix": "_r60", "priority": -67}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 4, "grow_policy": "Depthwise", "l2_leaf_reg": 1.5734131496361856, "learning_rate": 0.08472519974533015, "max_ctr_complexity": 3, "one_hot_max_size": 2, "ag_args": {"name_suffix": "_r6", "priority": -72}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 7, "grow_policy": "Depthwise", "l2_leaf_reg": 4.43335055453705, "learning_rate": 0.055406199833457785, "max_ctr_complexity": 5, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r180", "priority": -76}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 7, "grow_policy": "SymmetricTree", "l2_leaf_reg": 4.835797074498082, "learning_rate": 0.03534026385152556, "max_ctr_complexity": 5, "one_hot_max_size": 10, "ag_args": {"name_suffix": "_r12", "priority": -83}, 'ag_args_fit':{'num_gpus':0}},
        {"depth": 6, "grow_policy": "SymmetricTree", "l2_leaf_reg": 3.637071465711953, "learning_rate": 0.04387418552563314, "max_ctr_complexity": 4, "one_hot_max_size": 5, "ag_args": {"name_suffix": "_r198", "priority": -90}, 'ag_args_fit':{'num_gpus':0}},
    ],
    "XGB": [
        {},
        {"colsample_bytree": 0.6917311125174739, "enable_categorical": False, "learning_rate": 0.018063876087523967, "max_depth": 10, "min_child_weight": 0.6028633586934382, "ag_args": {"name_suffix": "_r33", "priority": -8}},
        {"colsample_bytree": 0.516652313273348, "enable_categorical": True, "learning_rate": 0.007158072983547058, "max_depth": 9, "min_child_weight": 0.8567068904025429, "ag_args": {"name_suffix": "_r98", "priority": -36}},
        {"colsample_bytree": 0.7452294043087835, "enable_categorical": False, "learning_rate": 0.038404229910104046, "max_depth": 7, "min_child_weight": 0.5564183327139662, "ag_args": {"name_suffix": "_r49", "priority": -57}},
        {"colsample_bytree": 0.7506621909633511, "enable_categorical": False, "learning_rate": 0.009974712407899168, "max_depth": 4, "min_child_weight": 0.9238550485581797, "ag_args": {"name_suffix": "_r31", "priority": -64}},
        {"colsample_bytree": 0.546186944730449, "enable_categorical": False, "learning_rate": 0.029357102578825213, "max_depth": 10, "min_child_weight": 1.1532008198571337, "ag_args": {"name_suffix": "_r34", "priority": -94}},
    ],
    "FASTAI": [
        {},
        {"bs": 256, "emb_drop": 0.5411770367537934, "epochs": 43, "layers": [800, 400], "lr": 0.01519848858318159, "ps": 0.23782946566604385, "ag_args": {"name_suffix": "_r191", "priority": -4}},
        {"bs": 128, "emb_drop": 0.44339037504795686, "epochs": 31, "layers": [400, 200, 100], "lr": 0.008615195908919904, "ps": 0.19220253419114286, "ag_args": {"name_suffix": "_r145", "priority": -15}},
        {"bs": 128, "emb_drop": 0.026897798530914306, "epochs": 31, "layers": [800, 400], "lr": 0.08045277634470181, "ps": 0.4569532219038436, "ag_args": {"name_suffix": "_r11", "priority": -21}},
        {"bs": 256, "emb_drop": 0.1508701680951814, "epochs": 46, "layers": [400, 200], "lr": 0.08794353125787312, "ps": 0.19110623090573325, "ag_args": {"name_suffix": "_r103", "priority": -25}},
        {"bs": 512, "emb_drop": 0.1567472816422661, "epochs": 41, "layers": [400, 200, 100], "lr": 0.06831450078222204, "ps": 0.4930900813464729, "ag_args": {"name_suffix": "_r37", "priority": -40}},
        {"bs": 1024, "emb_drop": 0.22771721361129746, "epochs": 38, "layers": [400], "lr": 0.0005383511954451698, "ps": 0.3734259772256502, "ag_args": {"name_suffix": "_r65", "priority": -54}},
        {"bs": 1024, "emb_drop": 0.4329361816589235, "epochs": 50, "layers": [400], "lr": 0.09501311551121323, "ps": 0.2863378667611431, "ag_args": {"name_suffix": "_r88", "priority": -55}},
        {"bs": 128, "emb_drop": 0.3171659718142149, "epochs": 20, "layers": [400, 200, 100], "lr": 0.03087210106068273, "ps": 0.5909644730871169, "ag_args": {"name_suffix": "_r160", "priority": -66}},
        {"bs": 128, "emb_drop": 0.3209601865656554, "epochs": 21, "layers": [200, 100, 50], "lr": 0.019935403046870463, "ps": 0.19846319260751663, "ag_args": {"name_suffix": "_r69", "priority": -71}},
        {"bs": 128, "emb_drop": 0.08669109226243704, "epochs": 45, "layers": [800, 400], "lr": 0.0041554361714983635, "ps": 0.2669780074016213, "ag_args": {"name_suffix": "_r138", "priority": -73}},
        {"bs": 512, "emb_drop": 0.05604276533830355, "epochs": 32, "layers": [400], "lr": 0.027320709383189166, "ps": 0.022591301744255762, "ag_args": {"name_suffix": "_r172", "priority": -75}},
        {"bs": 1024, "emb_drop": 0.31956392388385874, "epochs": 25, "layers": [200, 100], "lr": 0.08552736732040143, "ps": 0.0934076022219228, "ag_args": {"name_suffix": "_r127", "priority": -80}},
        {"bs": 256, "emb_drop": 0.5117456464220826, "epochs": 21, "layers": [400, 200, 100], "lr": 0.007212882302137526, "ps": 0.2747013981281539, "ag_args": {"name_suffix": "_r194", "priority": -82}},
        {"bs": 256, "emb_drop": 0.06099050979107849, "epochs": 39, "layers": [200], "lr": 0.04119582873110387, "ps": 0.5447097256648953, "ag_args": {"name_suffix": "_r4", "priority": -85}},
        {"bs": 1024, "emb_drop": 0.5074958658302495, "epochs": 42, "layers": [200, 100, 50], "lr": 0.026342427824862867, "ps": 0.34814978753283593, "ag_args": {"name_suffix": "_r187", "priority": -91}},
    ],
    "RF": [
        {"criterion": "squared_error", "ag_args": {"name_suffix": "MSE", "problem_types": ["regression", "quantile"]}},
    ],
    "XT": [
        {"criterion": "squared_error", "ag_args": {"name_suffix": "MSE", "problem_types": ["regression", "quantile"]}},
    ],
    "KNN": [
        {"weights": "uniform", "ag_args": {"name_suffix": "Unif"}},
        {"weights": "distance", "ag_args": {"name_suffix": "Dist"}},
    ],
}


index_map_reverse = {v: k for k, v in index_map.items()}


def convert_to_csi_code(market):
    if market in index_map_reverse:
        return index_map_reverse[market]
    return 'csi' + market


def convert_to_market_code(csi_code):
    if csi_code in index_map:
        return index_map[csi_code]
    return csi_code[3:]


def init_config():
    env_path = os.getenv('ENV_PATH', '.env')
    load_dotenv(os.path.join(os.getcwd(), env_path))
    for key, value in config_info.items():
        env_value = os.getenv(key, value)
        if isinstance(value, bool):
            if isinstance(env_value, str):
                env_value_lower = env_value.lower()
                if env_value_lower in ('true', '1', 't'):
                    config_info[key] = True
                elif env_value_lower in ('false', '0', 'f'):
                    config_info[key] = False
                else:
                    config_info[key] = env_value
            else:
                config_info[key] = env_value
        elif isinstance(value, int):
            try:
                config_info[key] = int(env_value)
            except ValueError:
                config_info[key] = value
        elif isinstance(value, float):
            try:
                config_info[key] = float(env_value)
            except ValueError:
                config_info[key] = value
        elif isinstance(value, list):
            if len(env_value) == 0:
                config_info[key] = []
            else:
                config_info[key] = env_value.split(',') if isinstance(env_value, str) else value
        else:
            config_info[key] = env_value
