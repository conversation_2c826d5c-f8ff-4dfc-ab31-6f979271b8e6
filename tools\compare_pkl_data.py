import sys
import pandas as pd


def compare_pkl_files(file1_path, file2_path):
    """
    比较两个pkl文件的差异
    
    Args:
        file1_path: 第一个pkl文件路径
        file2_path: 第二个pkl文件路径
    """
    # 读取两个pkl文件
    df1 = pd.read_pickle(file1_path)
    df2 = pd.read_pickle(file2_path)
    # 获取股票代码列表
    stocks1 = set(df1.index.get_level_values(1).unique())
    stocks2 = set(df2.index.get_level_values(1).unique())
    
    # 检查独有的股票代码
    only_in_1 = stocks1 - stocks2
    only_in_2 = stocks2 - stocks1
    
    print(f"仅在文件1中存在的股票代码: {len(only_in_1)}个")
    print(only_in_1)
    print(f"\n仅在文件2中存在的股票代码: {len(only_in_2)}个") 
    print(only_in_2)
    
    # 检查列的差异
    cols1 = set(df1.columns)
    cols2 = set(df2.columns)
    
    print(f"\n仅在文件1中存在的列: {cols1 - cols2}")
    print(f"仅在文件2中存在的列: {cols2 - cols1}")
    
    # 检查共同列的数据差异
    common_cols = cols1.intersection(cols2)
    common_stocks = stocks1.intersection(stocks2)
    
    print("\n共同列的数据差异:")
    # 存储所有列的差异信息
    col_diffs = []
    
    for col in common_cols:
        # 选取共同股票的数据进行比较
        mask1 = df1.index.get_level_values(1).isin(common_stocks)
        mask2 = df2.index.get_level_values(1).isin(common_stocks)
        
        data1 = df1[mask1][col]
        data2 = df2[mask2][col]
        
        # 计算差异
        if data1.dtype in ['float64', 'int64']:
            diff_mask = (data1 - data2).abs() > 1e-20
            diff_count = diff_mask.sum()
            
            if diff_count > 0:
                # 计算差异的均值和方差
                diff_values = (data1 - data2)[diff_mask]
                diff_mean = diff_values.mean()
                diff_std = diff_values.std()
                
                # 分析差异的时间分布
                diff_dates = diff_values.index.get_level_values(0)
                date_counts = diff_dates.value_counts().sort_index()
                
                col_diffs.append({
                    'col': col,
                    'diff_count': diff_count,
                    'diff_mean': diff_mean,
                    'diff_std': diff_std,
                    'date_counts': date_counts
                })
    
    print(f"\n总共有 {len(col_diffs)} 列存在差异")
    # 按差异条数排序并打印
    for diff_info in sorted(col_diffs, key=lambda x: x['diff_count'], reverse=True):
        print(f"\n列 {diff_info['col']} 有 {diff_info['diff_count']} 个数值差异")
        print(f"差异均值: {diff_info['diff_mean']:.6f}")
        print(f"差异标准差: {diff_info['diff_std']:.6f}")
        
        date_counts = diff_info['date_counts']
        if len(date_counts) > 0:
            print(f"差异主要集中在时间段: {date_counts.index[0]} 到 {date_counts.index[-1]}")
            # 找出差异最多的前3个时间点
            top_dates = date_counts.nlargest(3)
            print("差异最多的时间点:")
            for date, count in top_dates.items():
                print(f"  {date}: {count}个差异")
    # 检查nan值
    print("\nNaN值统计:")
    for df, name in [(df1, "文件1"), (df2, "文件2")]:
        print(f"\n{name}中各列的NaN值比例:")
        nan_stats = df.isna().sum() / len(df) * 100
        for col, pct in nan_stats[nan_stats > 5].items():  # 只显示NaN比例超过5%的列
            print(f"{col}: {pct:.2f}%")
            
    # 检查时间范围
    dates1 = df1.index.get_level_values(0).unique()
    dates2 = df2.index.get_level_values(0).unique()
    
    print(f"\n文件1的时间范围: {dates1.min()} 到 {dates1.max()}")
    print(f"文件2的时间范围: {dates2.min()} 到 {dates2.max()}")


if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("请提供两个pkl文件路径作为参数")
        sys.exit(1)
    compare_pkl_files(sys.argv[1], sys.argv[2])
