{"cells": [{"cell_type": "code", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["import util\n", "import os\n", "os.chdir('runtime')\n", "util.init_qlib()\n", "util.init_logger()"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["import beta_adjusted_alpha as ba\n", "import alpha_101 as a101\n", "import importlib\n", "importlib.reload(ba)\n", "importlib.reload(a101)\n", "\n", "start = '20200101'\n", "end = '20201231'\n", "\n", "ba.research_adjusted_alpha(start, end, selected_alpha_names=['gtja019'])"], "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 4}