import traceback
from manual_index import CustomIndexGenerator
from tools.dump_bin import Dump<PERSON><PERSON><PERSON><PERSON>, DumpDataUpdate
import tushare as ts
import fire
import pandas as pd
import os
import time
import pickle
from tqdm import tqdm
from datetime import datetime, timedelta

from util import init_qlib
import shutil


class StockAdditionalDataFetcher:
    # Initialize Tushare with your token
    def __init__(self):
        ts.set_token(os.environ['TUSHARE'])
        self.pro = ts.pro_api()

        # Cache for fetched data to avoid redundant API calls
        self.fetched_data_cache = {}

        # Load cache from file if it exists
        self.fetched_cache_file = 'fetched_data_cache.pkl'
        if os.path.exists(self.fetched_cache_file):
            with open(self.fetched_cache_file, 'rb') as f:
                self.fetched_data_cache = pickle.load(f)

        # API call tracking
        self.last_api_call_time = datetime.now() - timedelta(minutes=1)
        self.api_calls_in_last_minute = 0

    def save_cache(self):
        with open(self.fetched_cache_file, 'wb') as f:
            pickle.dump(self.fetched_data_cache, f)

    def rate_limit(self, max=25):
        now = datetime.now()
        if (now - self.last_api_call_time).total_seconds() >= 60:
            # Reset the counter every minute
            self.api_calls_in_last_minute = 0
            self.last_api_call_time = now
        if self.api_calls_in_last_minute >= max:
            # If the limit is reached, wait until 1 minute has passed
            time_to_wait = 60 - (now - self.last_api_call_time).total_seconds()
            time.sleep(time_to_wait)
            self.api_calls_in_last_minute = 0
            self.last_api_call_time = datetime.now()
        # Increment the API call counter
        self.api_calls_in_last_minute += 1

    def fetch_index_by_level(self, start_date, end_date, level):
        self.rate_limit(100)
        index_data = self.pro.index_classify(level=level, src='SW2021')
        results = []
        for _, row in index_data.iterrows():
            index_code = row['index_code']
            self.rate_limit(100)
            res = self.pro.sw_daily(ts_code=index_code, start_date=start_date, end_date=end_date)
            results.append(res)
        whole_res = pd.concat(results)
        dump_file = f'{level}_index_data.csv'
        org_datas = pd.read_csv(dump_file) if os.path.exists(dump_file) else pd.DataFrame()
        whole_res = pd.concat([org_datas, whole_res])
        whole_res = whole_res.drop_duplicates()
        whole_res.to_csv(f'{level}_index_data.csv', index=False)

    def fetch_index(self, start_date: str, end_date: str):
        self.fetch_index_by_level(start_date, end_date, 'L1')
        self.fetch_index_by_level(start_date, end_date, 'L2')

    def dump_index_to_qlib(self, level, qlib_dir, index_name, start_date, end_date=None):
        start_date = str(start_date)
        if end_date is not None:
            end_date = str(end_date)
        else:
            today = datetime.today()
            if today.weekday() >= 5:
                last_work_date = today - timedelta(days=today.weekday() - 4)
            else:
                last_work_date = today
            end_date = last_work_date.strftime('%Y%m%d')
        index_data = self.pro.index_classify(level=level, src='SW2021')

        tmp_dir = os.path.join(os.getcwd(), 'tmp')
        if os.path.exists(tmp_dir):
            shutil.rmtree(tmp_dir)
        os.makedirs(tmp_dir)

        index_dates = {}

        for _, row in index_data.iterrows():
            index_code_full = row['index_code']
            index_code = index_code_full.split('.')[0]
            self.rate_limit(100)
            res = self.pro.sw_daily(ts_code=index_code_full, start_date=start_date, end_date=end_date,
                                    fields='ts_code,trade_date,open,close,high,low,vol,amount')

            if len(res) == 0:
                continue
            res = res.sort_values(by='trade_date')
            res = res.rename(columns={'trade_date': 'date', 'ts_code': 'symbol', 'vol': 'volume'})
            index_dates[index_code] = res['date'].min(), res['date'].max()
            res['date'] = pd.to_datetime(res['date'], format='%Y%m%d').dt.strftime('%Y/%m/%d')
            res['change'] = res['close'].pct_change()
            res['vwap'] = (res['open'] + res['close'] + res['high'] + res['low']) / 4
            res['adjclose'] = res['close']
            res['factor'] = 1.0
            res['symbol'] = index_code
            res.to_csv(os.path.join(tmp_dir, f'{index_code}.csv'), index=False)

        DumpDataUpdate(csv_path=tmp_dir, qlib_dir=qlib_dir,
                       include_fields='open,close,high,low,volume,amount,change,vwap,adjclose,factor').dump()

        instr_dir = os.path.join(qlib_dir, 'instruments')
        os.makedirs(instr_dir, exist_ok=True)

        periods = CustomIndexGenerator.get_half_year_periods(start_date, end_date)
        result = {}
        for period_start, period_end in periods:
            index_list = []
            for index_code, (index_start, index_end) in index_dates.items():
                if index_start <= period_start and index_end >= period_end:
                    index_list.append(index_code)
            result[f"{period_start}_{period_end}"] = index_list
        CustomIndexGenerator.dump_qlib_instruments(qlib_dir, start_date, index_name, result)

    def process_index_data(self, path_index_data, path_featured_index_data):
        df = pd.read_csv(path_index_data, parse_dates=['trade_date'], dtype={'ts_code': str})

        # 确保数据按ts_code和trade_date排序
        df = df.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)

        # 定义需要计算百分位的列和窗口
        columns_to_percentile = ['pe', 'pb', 'float_mv']
        windows_percentile = [10, 20, 60, 250, 500]

        # 计算百分位指标（从小到大）
        for window in windows_percentile:
            for col in columns_to_percentile:
                percentile_col = f'{col}_pct_{window}d'
                df[percentile_col] = df.groupby('ts_code')[col].transform(
                    lambda x: x.rolling(window, min_periods=1).apply(
                        lambda y: y.rank(pct=True).iloc[-1], raw=False
                    )
                )

        # 计算每日收益率
        df['daily_return'] = df.groupby('ts_code')['close'].pct_change()

        # 定义窗口列表用于自定义公式
        window_sizes = [5, 10, 20, 30, 60]

        for window in window_sizes:
            # 计算收益率的均值和标准差
            df[f'return_mean_{window}d'] = df.groupby('ts_code')['daily_return'].transform(
                lambda x: x.rolling(window, min_periods=1).mean()
            )
            df[f'return_std_{window}d'] = df.groupby('ts_code')['daily_return'].transform(
                lambda x: x.rolling(window, min_periods=1).std()
            )

            # 计算成交量的均值和标准差，并与当前成交量的比值
            df[f'volume_mean_{window}d_ratio'] = df.groupby('ts_code')['vol'].transform(
                lambda x: x.rolling(window, min_periods=1).mean() / x
            )
            df[f'volume_std_{window}d_ratio'] = df.groupby('ts_code')['vol'].transform(
                lambda x: x.rolling(window, min_periods=1).std() / x
            )

        # 保存处理后的数据到新的CSV文件
        df.to_csv(path_featured_index_data, index=False)
        print(f"处理完成，结果已保存到 {path_featured_index_data}")

    def process_add_data(self, add_info_data_path, path_featured_index_data, add_info_feature_path):
        processed_stock = pd.read_csv(path_featured_index_data, parse_dates=['trade_date'], dtype={'ts_code': str})

        # 读取第二个CSV文件
        sector_df = pd.read_csv(add_info_data_path, parse_dates=['date'],
                                dtype={'stock_code': str, 'sector_code': str})

        # 确保数据按stock_code和date排序
        sector_df = sector_df.sort_values(['stock_code', 'date']).reset_index(drop=True)

        # 定义需要计算百分位的列和窗口
        columns_to_percentile = ['pe_ttm', 'ps_ttm', 'gx_ttm']
        windows_percentile = [10, 20, 60, 250, 500]

        # 计算百分位指标（从小到大）
        for window in windows_percentile:
            for col in columns_to_percentile:
                percentile_col = f'{col}_pct_{window}d'
                sector_df[percentile_col] = sector_df.groupby('stock_code')[col].transform(
                    lambda x: x.rolling(window, min_periods=1).apply(
                        lambda y: y.rank(pct=True).iloc[-1], raw=False
                    )
                )

        # 计算每日收益率（如果需要，可以类似于第一个函数）
        # 这里不明确是否需要，暂不计算

        # 准备合并：确保日期格式一致
        # 假设 'date' 和 'trade_date' 已经被解析为 datetime 类型

        # 为避免合并后列名冲突，给处理后的股票数据添加前缀
        processed_stock_renamed = processed_stock.rename(
            columns=lambda x: f'stock_{x}' if x not in ['ts_code', 'trade_date'] else x)

        # 合并数据
        merged_df = sector_df.merge(
            processed_stock_renamed,
            left_on=['sector_code', 'date'],
            right_on=['ts_code', 'trade_date'],
            how='left',
            suffixes=('', '_stock')
        )

        # 去掉原始的sector_开头的列和close列
        columns_to_drop = [col for col in sector_df.columns if col.startswith('sector_')] + ['close']
        merged_df = merged_df.drop(columns=columns_to_drop)

        # 如果不需要保留 'ts_code' 和 'trade_date'，可以进一步删除
        merged_df = merged_df.drop(
            columns=['ts_code', 'trade_date', 'pe', 'pb', 'ps', 'sector', 'stock_name', 'stock_open',
                     'stock_low', 'stock_high', 'stock_close', 'stock_change', 'stock_pct_change', 'stock_vol',
                     'stock_ammount',
                     'stock_pct_change', 'stock_vol', 'stock_amount', 'stock_pe', 'stock_pb', 'stock_float_mv',
                     'stock_total_mv', 'stock_daily_return'
                     ], errors='ignore')

        # 保存处理后的数据到新的CSV文件
        merged_df.to_csv(add_info_feature_path, index=False)
        print(f"处理完成，结果已保存到 {add_info_feature_path}")

    def index_weight(self, start_date, end_date, index_code):
        start_date = datetime.strptime(start_date, "%Y%m%d")
        end_date = datetime.strptime(end_date, "%Y%m%d")

        stock_list = pd.DataFrame()
        delta = timedelta(days=182)
        current_start_date = start_date

        while current_start_date < end_date:
            current_end_date = min(current_start_date + delta, end_date)

            formatted_start_date = current_start_date.strftime("%Y%m%d")
            formatted_end_date = current_end_date.strftime("%Y%m%d")

            temp_stock_list = self.pro.index_weight(index_code=index_code, start_date=formatted_start_date,
                                                    end_date=formatted_end_date)

            stock_list = pd.concat([stock_list, temp_stock_list])
            current_start_date = current_end_date + timedelta(days=1)

        return stock_list

    def fetch_data_with_retry(self, stock, start_date, end_date, existing_data, results, index_cache):
        sleep_time = 1  # 初始等待时间为1秒

        while True:  # 无限循环，直到成功
            try:
                # Skip if the stock data for the given date range has already been fetched
                if stock in self.fetched_data_cache and self.fetched_data_cache[stock] == (start_date, end_date):
                    return

                self.rate_limit()

                # Fetch factor data
                factor_data = self.pro.stk_factor_pro(ts_code=stock, start_date=start_date, end_date=end_date,
                                                      fields="ts_code,trade_date,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,free_share,total_mv,circ_mv")

                # Fetch sector info
                sector_info = self.pro.stock_basic(ts_code=stock, fields='ts_code,industry')
                sector_name = sector_info['industry'].iloc[0] if not sector_info.empty else 'Unknown'

                # Fetch sector performance
                sector_index = self.pro.index_member_all(ts_code=stock, fields='l2_code')
                if len(sector_index) > 0:
                    index_code = sector_index['l2_code'].iloc[0]
                    sector_performance = index_cache[index_cache['ts_code'] == index_code]
                    sector_performance = sector_performance[(sector_performance['trade_date'] >= start_date) &
                                                            (sector_performance['trade_date'] <= end_date)]
                else:
                    sector_performance = None
                # Fetch performance data
                cyq_perf_data = self.pro.cyq_perf(ts_code=stock, start_date=start_date, end_date=end_date,
                                                  fields='ts_code,trade_date,cost_5pct,cost_15pct,cost_50pct,cost_85pct,cost_95pct,weight_avg,winner_rate')

                # Fetch daily stock data
                daily_data = self.pro.daily(ts_code=stock, start_date=start_date, end_date=end_date,
                                            fields='trade_date,close')

                # Process factor data
                stock_factors = factor_data[factor_data['ts_code'] == stock]
                for _, row in stock_factors.iterrows():
                    current_date = row['trade_date']
                    stock_code = stock[:6]

                    # Skip if data for this date already exists
                    if not existing_data.empty and ((existing_data['stock_code'] == stock_code) &
                                                    (existing_data['date'] == current_date)).any():
                        continue

                    result = {
                        'date': current_date,
                        'stock_code': stock_code,
                        'pe': row['pe'],
                        'pe_ttm': row['pe_ttm'],
                        'pb': row['pb'],
                        'ps': row['ps'],
                        'ps_ttm': row['ps_ttm'],
                        'gx_ratio': row['dv_ratio'],
                        'gx_ttm': row['dv_ttm'],
                        'sector': sector_name
                    }

                    # Add sector performance data if available
                    if sector_performance is not None:
                        sector_data = sector_performance[sector_performance['trade_date'] == current_date]
                        if not sector_data.empty:
                            result.update({
                                'sector_code': sector_data['ts_code'].iloc[0],
                                'sector_open': sector_data['open'].iloc[0],
                                'sector_close': sector_data['close'].iloc[0],
                                'sector_high': sector_data['high'].iloc[0],
                                'sector_low': sector_data['low'].iloc[0],
                                'sector_vol': sector_data['vol'].iloc[0],
                                'sector_amount': sector_data['amount'].iloc[0],
                                'sector_pe': sector_data['pe'].iloc[0],
                                'sector_pb': sector_data['pb'].iloc[0],
                                'sector_pct_change': sector_data['pct_change'].iloc[0]
                            })

                    # Add performance data if available
                    cyq_data = cyq_perf_data[cyq_perf_data['trade_date'] == current_date]
                    if not cyq_data.empty:
                        close_price = daily_data[daily_data['trade_date'] == current_date]['close'].iloc[0] if not \
                            daily_data[daily_data['trade_date'] == current_date].empty else None
                        if close_price:
                            result.update({
                                'cost_5pct_change': ((cyq_data['cost_5pct'].iloc[0] - close_price) / close_price) * 100,
                                'cost_15pct_change': ((cyq_data['cost_15pct'].iloc[
                                                           0] - close_price) / close_price) * 100,
                                'cost_50pct_change': ((cyq_data['cost_50pct'].iloc[
                                                           0] - close_price) / close_price) * 100,
                                'cost_85pct_change': ((cyq_data['cost_85pct'].iloc[
                                                           0] - close_price) / close_price) * 100,
                                'cost_95pct_change': ((cyq_data['cost_95pct'].iloc[
                                                           0] - close_price) / close_price) * 100,
                                'weight_avg_change': ((cyq_data['weight_avg'].iloc[
                                                           0] - close_price) / close_price) * 100,
                                'winner_rate': cyq_data['winner_rate'].iloc[0]
                            })

                    daily_close_data = daily_data[daily_data['trade_date'] == current_date]
                    if not daily_close_data.empty:
                        result['close'] = daily_close_data['close'].iloc[0]

                    results.append(result)

                # Update fetched data cache
                self.fetched_data_cache[stock] = (start_date, end_date)
                self.save_cache()

                return

            except Exception as e:
                print(f"Error processing stock {stock}: {e} Traceback: {traceback.format_exc()}")
                print(f"Retrying after {sleep_time} seconds...")

                time.sleep(sleep_time)
                sleep_time += 5  # 每次增加5秒

    def fetch(self, start_date: str, end_date: str, index_code: str):
        start_date = str(start_date)
        end_date = str(end_date)
        result_file = f'{index_code}_additional_data.csv'
        # Map the index code if needed
        if index_code == '000300':
            index_code = '399300.SZ'
        elif index_code.startswith('000'):
            index_code = f'{index_code}.SH'
        elif index_code.startswith('399'):
            index_code = f'{index_code}.SZ'

        # Check if the result file exists

        existing_data = pd.DataFrame()
        if os.path.exists(result_file):
            existing_data = pd.read_csv(result_file)

        # Fetch the stock list for the given index
        # self.rate_limit()
        stock_list = self.index_weight(start_date, end_date, index_code)
        results = []

        index_cache = pd.read_csv(f'L2_index_data.csv', dtype={'ts_code': str, 'trade_date': str})

        batch_size = 30
        stock_list = stock_list[['con_code']].drop_duplicates()
        for i in tqdm(range(0, len(stock_list), batch_size)):
            batch = stock_list['con_code'][i:i + batch_size]
            cur_time = time.localtime(time.time() + 60)
            for stock in batch:
                self.fetch_data_with_retry(stock, start_date, end_date, existing_data, results, index_cache)

            # 等待时间到达cur_time + 1分钟
            while time.localtime(time.time()) < cur_time:
                time.sleep(1)

        if results:
            df = pd.DataFrame(results)
            df.to_csv(result_file, mode='a', header=not os.path.exists(result_file), index=False)


if __name__ == '__main__':
    init_qlib()
    fire.Fire(StockAdditionalDataFetcher)
