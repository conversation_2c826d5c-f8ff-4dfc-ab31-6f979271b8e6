import datetime
import os
import time

import fire
import pandas as pd
import tushare as ts
from dateutil.relativedelta import relativedelta
from qlib.data import D
from tqdm import tqdm

from configs import config_info
from util import init_logger, init_qlib


class CustomIndexGenerator:
    def __init__(self, qlib_data_path=None, cache_dir='cache'):
        ts.set_token(os.environ['TUSHARE'])
        self.pro = ts.pro_api()
        self.cache_dir = cache_dir
        if qlib_data_path is None:
            qlib_data_path = config_info['qlib_url']
        self.qlib_data_path = qlib_data_path

        # 创建缓存目录
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

    def get_previous_trading_day(self, date_str):
        """获取指定日期的前一个交易日"""
        cal = D.calendar(end_time=date_str, freq='day')

        if cal[-1].strftime('%Y%m%d') == date_str:
            return date_str  # 日期本身就是交易日
        else:
            prev_date = max([d for d in cal if d.strftime('%Y%m%d') < date_str])
            return prev_date.strftime('%Y%m%d')

    @staticmethod
    def get_half_year_periods(start_date, e_date):
        """
        获取从开始日期到结束日期的半年区间列表
        Args:
            start_date: 开始日期，格式为'YYYYMMDD'
            e_date: 结束日期，格式为'YYYYMMDD'
        Returns:
            periods: 包含(期间开始日期, 期间结束日期)元组的列表，日期格式为'YYYYMMDD'
        """
        periods = []
        start = datetime.datetime.strptime(start_date, '%Y%m%d')
        end = datetime.datetime.strptime(e_date, '%Y%m%d')

        current = start

        while current <= end:
            year = current.year
            month = current.month

            # 确定当前所在的半年
            if month <= 6:
                half_start = datetime.datetime(year, 1, 1)
                half_end = datetime.datetime(year, 6, 30)
                next_half_start = datetime.datetime(year, 7, 1)
            else:
                half_start = datetime.datetime(year, 7, 1)
                half_end = datetime.datetime(year, 12, 31)
                next_half_start = datetime.datetime(year + 1, 1, 1)

            # 如果是第一个期间，使用start_date作为期间开始
            if current > half_start:
                period_start = current
            else:
                period_start = half_start

            # 如果半年的结束日期超过总结束日期，则使用总结束日期
            if half_end > end:
                period_end = end
            else:
                period_end = half_end

            # 添加期间到列表
            periods.append((
                period_start.strftime('%Y%m%d'),
                period_end.strftime('%Y%m%d')
            ))

            # 移到下一个半年
            current = next_half_start

        return periods

    @staticmethod
    def get_month_periods(start_date, e_date):
        """
        获取从开始日期到结束日期的自然月区间列表
        Args:
            start_date: 开始日期，格式为'YYYYMMDD'
            e_date: 结束日期，格式为'YYYYMMDD'
        Returns:
            periods: 包含(期间开始日期, 期间结束日期)元组的列表，日期格式为'YYYYMMDD'
        """
        periods = []
        start = datetime.datetime.strptime(start_date, '%Y%m%d')
        end = datetime.datetime.strptime(e_date, '%Y%m%d')

        current = start

        while current <= end:
            # 获取当月第一天
            period_start = datetime.datetime(current.year, current.month, 1)

            # 获取当月最后一天
            if current.month == 12:
                next_month = datetime.datetime(current.year + 1, 1, 1)
            else:
                next_month = datetime.datetime(current.year, current.month + 1, 1)
            period_end = next_month - datetime.timedelta(days=1)

            # 如果结束日期在当前月内，则使用结束日期
            if period_end > end:
                period_end = end

            # 添加期间到列表
            periods.append((
                period_start.strftime('%Y%m%d'),
                period_end.strftime('%Y%m%d')
            ))

            # 移到下个月第一天
            current = next_month

        return periods

    def cache_data(self, func):
        """数据缓存装饰器"""

        def wrapper(*args, **kwargs):
            cache_key = func.__name__ + '_' + '_'.join(map(str, args)) + '_' + '_'.join(
                [f"{k}_{v}" for k, v in kwargs.items()])
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")
            if os.path.exists(cache_file):
                df = pd.read_csv(cache_file, dtype={'ts_code': str})
            else:
                df = func(*args, **kwargs)
                df.to_csv(cache_file, index=False)
            return df

        return wrapper

    def get_st_stocks(self, date):
        """获取所有股票的名称变更信息（包括ST变更），并缓存"""

        @self.cache_data
        def _get_st_stocks(date):
            # 获取所有股票的名称变更信息
            start_date = (datetime.datetime.strptime(date, '%Y%m%d') - datetime.timedelta(days=365 * 4)).strftime(
                '%Y%m%d')
            df = self.pro.namechange(start_date=start_date, end_date=date,
                                     fields='ts_code,name,start_date,end_date,ann_date,change_reason')
            df = df[(df['change_reason'] == 'ST') | (df['change_reason'] == '*ST')] \
                .sort_values(by='end_date', ascending=True).drop_duplicates(subset=['ts_code', 'start_date'],
                                                                            keep='first')
            time.sleep(1)
            return df

        df = _get_st_stocks(date)
        df['start_date'] = df['start_date'].astype(str)
        df['end_date'] = df['end_date'].astype(str)
        result = df[(df['start_date'] <= date) & (df['end_date'].isna() | (df['end_date'] > date))]
        return result['ts_code'].tolist()

    def get_stock_basic(self):
        """获取指定日期的股票基本信息"""

        @self.cache_data
        def _get_stock_basic():
            df = self.pro.stock_basic(exchange='', list_status='L',
                                      fields='ts_code,symbol,name,area,industry,list_date')
            return df

        return _get_stock_basic()

    def get_daily_basic(self, trade_date):
        """获取指定交易日的每日指标，包括总市值等"""

        @self.cache_data
        def _get_daily_basic(trade_date):
            df = self.pro.daily_basic(trade_date=trade_date,
                                      fields='ts_code,trade_date,close,total_mv,circ_mv,turnover_rate,pe,pe_ttm,pb')
            return df

        return _get_daily_basic(trade_date)

    def get_index_weight(self, index_code, trade_date):
        """获取指定日期的指数成分股"""

        @self.cache_data
        def _get_index_weight(index_code, trade_date):
            prev_month_date = datetime.datetime.strptime(trade_date, '%Y%m%d') + relativedelta(months=-1)
            prev_day_date = datetime.datetime.strptime(trade_date, '%Y%m%d') + datetime.timedelta(days=-1)
            prev_month_date = prev_month_date.strftime('%Y%m%d')
            prev_day_date = prev_day_date.strftime('%Y%m%d')
            df = self.pro.index_weight(index_code=index_code, start_date=prev_month_date, end_date=prev_day_date)
            return df

        return _get_index_weight(index_code, trade_date)

    def get_daily_is_st(self, trade_date):
        """获取指定交易日的每日行情数据，包括是否ST；本例里演示用，不做实际调用。"""

        @self.cache_data
        def _get_daily(trade_date):
            df = self.pro.daily(trade_date=trade_date, fields='ts_code,is_st')
            return df

        return _get_daily(trade_date)

    def filter_st_stocks(self, stock_list, trade_date):
        """过滤ST股票"""
        st_stocks = self.get_st_stocks(trade_date)
        return [code for code in stock_list if code not in st_stocks]

    def filter_gem_stock(self, stock_list):
        """过滤创业板股票"""
        filtered_list = []
        for code in stock_list:
            if not code.startswith('300') and not code.startswith('301'):
                filtered_list.append(code)
        return filtered_list

    def filter_star_stock(self, stock_list):
        """过滤科创板股票"""
        filtered_list = []
        for code in stock_list:
            if not code.startswith('688'):
                filtered_list.append(code)
        return filtered_list

    def filter_bse_stock(self, stock_list):
        """过滤北交所股票"""
        filtered_list = []
        for code in stock_list:
            if (not code.startswith('8')) and (not code.startswith('4')):
                filtered_list.append(code)
        return filtered_list

    def filter_by_industry(self, stock_df, industries):
        """根据所属行业过滤股票"""
        return stock_df[stock_df['industry'].isin(industries)]['ts_code'].tolist()

    def filter_by_market_cap(self, daily_basic_df, method, **kwargs):
        """根据市值大小过滤股票"""
        if method == 'percentile':
            start_pct = kwargs.get('start_pct', 0)
            end_pct = kwargs.get('end_pct', 100)
            total_mv = daily_basic_df.sort_values(by='total_mv', ascending=False)
            start_idx = int(len(total_mv) * start_pct / 100)
            end_idx = int(len(total_mv) * end_pct / 100)
            return total_mv.iloc[start_idx:end_idx]
        elif method == 'range':
            min_mv = kwargs.get('min_mv', 0)
            max_mv = kwargs.get('max_mv', float('inf'))
            mv_filtered = daily_basic_df[
                (daily_basic_df['total_mv'] >= min_mv) & (daily_basic_df['total_mv'] <= max_mv)]
            return mv_filtered
        elif method == 'top_n':
            n = kwargs.get('n', 10)
            start = kwargs.get('start', 0)
            total_mv = daily_basic_df.sort_values(by='total_mv', ascending=False)
            return total_mv.head(start + n).tail(n)
        elif method == 'bottom_n':
            n = kwargs.get('n', 10)
            start = kwargs.get('start', 0)
            total_mv = daily_basic_df.sort_values(by='total_mv', ascending=True)
            return total_mv.head(start + n).tail(n)
        else:
            return daily_basic_df

    def filter_by_pe_ratio(self, daily_basic_df):
        """根据市盈率过滤股票，PE > 0"""
        return daily_basic_df[daily_basic_df['pe'] > 0]

    def get_income_statements(self, periods):
        """获取指定财务报告期的财务报表数据"""
        data_frames = []
        for period in periods:
            @self.cache_data
            def _get_income_statements(period):
                df = self.pro.income_vip(period=period,
                                         fields='ts_code,ann_date,end_date,report_type,operate_profit,n_income_attr_p')
                return df

            ret = _get_income_statements(period)
            ret['period'] = period
            data_frames.append(ret)
        result_df = pd.concat(data_frames)
        return result_df

    def filter_by_net_profit_growth(self, stock_list, trading_day, operate_profit_growth_ratio=0, **kwargs):
        """
        根据净利润增长过滤股票：
        - 收集最近两年可能的财报期，如 2022-03-31, 2022-06-30, 2022-09-30, 2022-12-31, 2023-03-31...
        - 下载这些财报数据后，先过滤掉公告日期(ann_date)大于当前交易日(trading_day)的内容
          (说明该财报尚未发布，不能使用)，再根据累计利润计算每个季度的实际营业利润，并选出最近两个季度进行增长比较。

        Args:
            stock_list: 股票列表，DataFrame格式，需包含'ts_code'列
            trading_day: 交易日，字符串格式如'20240427'
            operate_profit_growth_ratio: 要求的本期营业利润相对上期的增长比例，如0.1表示要求增长10%
        """
        # 将 trading_day 转换为 datetime 对象，方便生成财报期
        date_dt = datetime.datetime.strptime(trading_day, '%Y%m%d')

        # 1) 收集最近两年可能的财报期
        possible_periods = []
        for y in [date_dt.year, date_dt.year - 1]:
            for (m, d) in [(3, 31), (6, 30), (9, 30), (12, 31)]:
                rp = datetime.datetime(y, m, d)
                # 只收集 <= trading_day 的日期(防止远未来的日期)
                if rp <= date_dt:
                    possible_periods.append(rp.strftime('%Y%m%d'))
        # 逆序排序，最新的 period 在前
        possible_periods.sort(reverse=True)

        if not possible_periods:
            print("无法找到最近两年内的财报期，跳过净利润增长过滤。")
            return stock_list

        # 2) 一次性获取这些 period 对应的财报数据
        income_df = self.get_income_statements(possible_periods)

        # 只保留当前股票池中的数据
        income_df = income_df[income_df['ts_code'].isin(stock_list['ts_code'])]

        # 3) 剔除公告日期为空或尚未发布(ann_date > trading_day)的行
        income_df = income_df.dropna(subset=['ann_date'])
        income_df = income_df[income_df['ann_date'].astype(int) <= int(trading_day)]

        # 4) 只保留合并报表 (report_type=1)
        income_df = income_df[income_df['report_type'].astype(int) == 1]

        if income_df.empty:
            print("筛选后的财报数据为空，跳过净利润增长过滤。")
            return stock_list

        # 5) 按ts_code和period升序排序，以便计算累计利润差值
        income_df = income_df.sort_values(['ts_code', 'period'], ascending=[True, True])

        # 6) 计算每个季度的实际营业利润
        # 对于每个ts_code，季度利润 = 当前累计利润 - 上一累计利润
        income_df['prev_operate_profit'] = income_df.groupby('ts_code')['operate_profit'].shift(1)
        income_df['quarter_profit'] = income_df['operate_profit'] - income_df['prev_operate_profit']

        # 7) 剔除无法计算季度利润的行（如第一个报表期）
        income_df = income_df.dropna(subset=['quarter_profit'])

        # 8) 排序为降序，以便选取最新的两个季度
        income_df = income_df.sort_values(['ts_code', 'period'], ascending=[True, False])

        # 9) 为每个股票分配排名，最新的季度为1，次新的季度为2
        income_df['rank'] = income_df.groupby('ts_code')['period'].rank(method='first', ascending=False)

        # 10) 只保留排名前两的季度利润
        income_df = income_df[income_df['rank'] <= 2]

        # 11) 将排名前两的季度利润转换为宽表格式
        income_pivot = income_df.pivot(index='ts_code', columns='rank', values='quarter_profit')

        # 重命名列以便理解
        income_pivot = income_pivot.rename(columns={1: 'curr_profit', 2: 'prev_profit'})

        # 12) 删除缺失值，确保每个股票都有最新和次新的两个季度利润
        income_pivot = income_pivot.dropna()

        # 13) 确保季度利润为数值类型
        income_pivot['curr_profit'] = pd.to_numeric(income_pivot['curr_profit'], errors='coerce')
        income_pivot['prev_profit'] = pd.to_numeric(income_pivot['prev_profit'], errors='coerce')
        income_pivot = income_pivot.dropna()

        if income_pivot.empty:
            print("没有股票满足有两个有效季度利润进行比较，跳过净利润增长过滤。")
            return stock_list

        # 14) 定义净利润增长检查函数
        def check_profit_growth(row):
            curr_profit = row['curr_profit']
            prev_profit = row['prev_profit']

            # 如果上期为负，本期为正，则一定满足增长要求
            if prev_profit < 0 and curr_profit > 0:
                return True
            # 如果上期为正，本期为负，则一定不满足增长要求
            elif prev_profit > 0 and curr_profit < 0:
                return False
            # 如果都为负，则要求亏损减少的比例超过增长要求
            elif prev_profit < 0 and curr_profit < 0:
                return (abs(curr_profit) - abs(prev_profit)) / abs(prev_profit) <= -operate_profit_growth_ratio
            # 如果都为正，则要求增长比例超过增长要求
            else:
                return (curr_profit - prev_profit) / abs(prev_profit) >= operate_profit_growth_ratio

        # 15) 应用过滤条件
        filtered_stocks = income_pivot[income_pivot.apply(check_profit_growth, axis=1)]

        # 16) 返回满足条件的股票列表
        return stock_list[stock_list['ts_code'].isin(filtered_stocks.index)]

    def filter_by_listing_date(self, stock_df, target_date):
        """过滤上市未满指定年限的股票"""
        # 将上市日期转换为 datetime
        stock_df['list_date'] = pd.to_datetime(stock_df['list_date'], format='%Y%m%d')
        # 计算上市时间
        target_date_dt = datetime.datetime.strptime(target_date, '%Y%m%d')
        index_list_before_year = config_info['index_list_before_year']
        min_list_date = target_date_dt - (
            relativedelta(years=index_list_before_year) if index_list_before_year > 0 else relativedelta(
                days=config_info['prepared_data_lens']))
        filtered_stocks = stock_df[stock_df['list_date'] <= min_list_date]['ts_code'].tolist()
        return filtered_stocks

    def get_index_stocks(self, index_code, trade_date):
        """根据所属指数过滤股票"""
        index_weights = self.get_index_weight(index_code, trade_date)
        return index_weights['con_code'].tolist()

    def get_filtered_stocks(self,
                            start_date,
                            end_date,
                            industries=None,
                            exclude_st=False,
                            exclude_gem=False,
                            exclude_star=False,
                            exclude_bse=False,
                            index_code=None,
                            market_cap_method=None,
                            positive_pe=False,
                            positive_growth=False,
                            operate_profit_growth_ratio=0,
                            consecutive_growth_quarters=1,
                            **kwargs):
        """
        获取指定日期范围内，每个自然半年或自然月符合条件的股票集合。
        若不指定 index_code => 按自然月(M)；否则按自然半年(H)
        其余新增的参数可以通过 kwargs 继续向下传递，避免在此显式声明。

        Args:
            start_date: 开始日期
            end_date: 结束日期
            industries: 行业列表
            exclude_st: 是否排除ST股票
            exclude_gem: 是否排除创业板
            exclude_star: 是否排除科创板
            exclude_bse: 是否排除北交所
            index_code: 指数代码
            market_cap_method: 市值过滤方法
            positive_pe: 是否要求PE为正
            positive_growth: 是否要求业绩增长
            operate_profit_growth_ratio: 营业利润增长比例要求
            consecutive_growth_quarters: 要求连续增长的季度数
            **kwargs: 其他参数
        """
        # 如果没有 index_code，就按照自然月(M)；否则按照自然半年(H)
        force_year = kwargs.get('force_year', False)
        force_year = force_year in ['True', 'true', '1', True]
        if force_year:
            periods = CustomIndexGenerator.get_half_year_periods(start_date, end_date)
        elif index_code is None or positive_growth or market_cap_method:
            periods = CustomIndexGenerator.get_month_periods(start_date, end_date)
        else:
            periods = CustomIndexGenerator.get_half_year_periods(start_date, end_date)

        result = {}
        for period_start, period_end in tqdm(periods, desc="处理时间区间"):
            # 获取股票列表
            stock_basic = self.get_stock_basic()
            stock_list = stock_basic['ts_code'].tolist()
            stock_basic_filtered = stock_basic[stock_basic['ts_code'].isin(stock_list)]
            stock_list = self.filter_by_listing_date(stock_basic_filtered, period_start)

            # 过滤ST股票
            if exclude_st:
                stock_list = self.filter_st_stocks(stock_list, period_start)

            # 根据行业过滤
            if industries:
                stock_basic_filtered = stock_basic[stock_basic['ts_code'].isin(stock_list)]
                stock_list = self.filter_by_industry(stock_basic_filtered, industries)

            # 过滤创业板
            if exclude_gem:
                stock_list = self.filter_gem_stock(stock_list)
            # 过滤科创板
            if exclude_star:
                stock_list = self.filter_star_stock(stock_list)
            # 过滤北交所
            if exclude_bse:
                stock_list = self.filter_bse_stock(stock_list)

            # 根据指数过滤
            if index_code:
                if index_code == '399006.SZ':
                    stock_list = [code for code in stock_list if code.startswith('30')]
                else:
                    index_stocks = self.get_index_stocks(index_code, period_start)
                    stock_list = [code for code in stock_list if code in index_stocks]

            trading_day = self.get_previous_trading_day(period_start)
            daily_basic = self.get_daily_basic(trading_day)
            daily_basic = daily_basic[daily_basic['ts_code'].isin(stock_list)]

            if market_cap_method:
                daily_basic = self.filter_by_market_cap(daily_basic, market_cap_method, **kwargs)
            if positive_pe:
                daily_basic = self.filter_by_pe_ratio(daily_basic)
            if positive_growth:
                daily_basic = self.filter_by_net_profit_growth(daily_basic, trading_day, operate_profit_growth_ratio,
                                                               consecutive_growth_quarters=consecutive_growth_quarters)

            daily_basic = daily_basic['ts_code'].tolist()

            result[f"{period_start}_{period_end}"] = daily_basic
        return result

    @staticmethod
    def tushare_code_to_qlib_code(ts_code):
        if '.' not in ts_code:
            return ts_code
        code, exchange = ts_code.split('.')
        if exchange == 'BJ':
            return 'BJ' + code
        elif exchange == 'SH':
            return 'SH' + code
        elif exchange == 'SZ':
            return 'SZ' + code
        else:
            raise ValueError(f"unknown exchange: {exchange}")

    def get_trading_days(self, start_date, end_date):
        """获取交易日列表"""
        cal = D.calendar(start_time=start_date, end_time=end_date, freq='day')
        return cal

    def expand_periods_to_trading_dates(self, stocks_by_period):
        """将每个周期的股票列表展开到交易日"""
        date_to_stocks = {}
        for period, stock_list in stocks_by_period.items():
            period_start, period_end = period.split('_')
            trading_days = self.get_trading_days(period_start, period_end)
            for date in trading_days:
                date_str = date.strftime('%Y-%m-%d')
                date_to_stocks[date_str] = stock_list
        return date_to_stocks

    def get_stock_data(self, stock_list, date):
        """获取指定日期股票的行情数据"""
        fields = ['$open', '$close', '$high', '$low', '$volume', '$amount']
        qlib_stock_list = [CustomIndexGenerator.tushare_code_to_qlib_code(ts_code) for ts_code in stock_list]
        qlib_stock_list = [code for code in qlib_stock_list if code is not None]
        if not qlib_stock_list:
            return pd.DataFrame()
        df = D.features(qlib_stock_list, fields, start_time=date, end_time=date)
        return df

    def compute_index_data(self, df):
        """计算指数的行情数据，等权重平均"""
        # df 的 MultiIndex 为 (instrument, datetime)
        # 需要按 datetime 分组然后平均
        index_data = df.groupby('datetime').mean()
        return index_data

    def get_index_data(self, date_to_stocks):
        """获取指数的行情数据"""
        index_data_list = []
        for date_str, stock_list in tqdm(date_to_stocks.items(), desc="获取指数数据"):
            date = date_str
            if not stock_list:
                continue
            df = self.get_stock_data(stock_list, date)
            if df.empty:
                continue
            index_df = self.compute_index_data(df)
            index_df.reset_index(inplace=True)
            index_df['date'] = date
            index_data_list.append(index_df)
        if index_data_list:
            index_data = pd.concat(index_data_list, ignore_index=True)
            return index_data
        else:
            return pd.DataFrame()

    def generate_custom_index(self,
                              start_date='20200101',
                              e_date='20201231',
                              industries=None,
                              exclude_st=False,
                              exclude_gem=False,
                              exclude_star=False,
                              exclude_bse=False,
                              positive_pe=False,
                              positive_growth=False,
                              operate_profit_growth_ratio=0,
                              consecutive_growth_quarters=1,
                              index_code=None,
                              market_cap_method=None,
                              n=50,
                              start=0,
                              custom_index_name='自定义指数',
                              **kwargs):
        """
        生成自定义指数并保存到 CSV 文件。
        会自动补全从 start_date 到第一个有效 period 开始日期之间的成分股数据。
        空的时间区间会被移除，使用最早有效区间的成分股来补全历史数据。
        """
        # 获取符合条件的股票集合
        stocks_by_period = self.get_filtered_stocks(
            start_date,
            e_date,
            industries=industries,
            exclude_st=exclude_st,
            exclude_gem=exclude_gem,
            exclude_star=exclude_star,
            exclude_bse=exclude_bse,
            index_code=index_code,
            market_cap_method=market_cap_method,
            n=n,
            start=start,
            positive_pe=positive_pe,
            positive_growth=positive_growth,
            operate_profit_growth_ratio=operate_profit_growth_ratio,
            consecutive_growth_quarters=consecutive_growth_quarters,
            **kwargs
        )

        # 移除空的时间区间
        return CustomIndexGenerator.dump_qlib_instruments(self.qlib_data_path,start_date, custom_index_name, stocks_by_period)

    @staticmethod
    def dump_qlib_instruments(qlib_data_path, start_date, custom_index_name, stocks_by_period):
        stocks_by_period = {k: v for k, v in stocks_by_period.items() if v}

        if not stocks_by_period:
            print("没有找到任何符合条件的股票，退出。")
            return

        # 获取最早的有效period及其成分股
        earliest_period = min(stocks_by_period.keys())
        earliest_period_start = earliest_period.split('_')[0]
        earliest_stocks = stocks_by_period[earliest_period]

        # 如果start_date早于最早的有效period，则补全历史数据
        if start_date < earliest_period_start:
            # 计算补全区间的结束日期（最早有效period开始的前一天）
            end_date_dt = datetime.datetime.strptime(earliest_period_start, '%Y%m%d') - datetime.timedelta(days=1)
            history_period_end = end_date_dt.strftime('%Y%m%d')

            # 验证earliest_stocks在start_date时是否有数据
            valid_stocks = []
            start_date_formatted = datetime.datetime.strptime(start_date, '%Y%m%d').strftime('%Y-%m-%d')

            for stock in earliest_stocks:
                qlib_code = CustomIndexGenerator.tushare_code_to_qlib_code(stock)
                if qlib_code:
                    try:
                        # 尝试获取该股票在start_date的数据
                        df = D.features([qlib_code], ['$close'],
                                        start_time=start_date_formatted,
                                        end_time=end_date_dt.strftime('%Y-%m-%d'))
                        if not df.empty:
                            valid_stocks.append(stock)
                    except Exception:
                        continue

            # 将补全的历史数据添加到stocks_by_period
            if valid_stocks:
                history_period = f"{start_date}_{history_period_end}"
                stocks_by_period[history_period] = valid_stocks

        # 将周期级别的成分股写入 qlib 的 instruments 文件
        constituents_file = os.path.join(qlib_data_path, 'instruments', f"{custom_index_name}.txt")
        with open(constituents_file, 'w') as f:
            for period, stock_list in sorted(stocks_by_period.items()):
                period_start, period_end = period.split('_')
                period_start_formatted = datetime.datetime.strptime(period_start, '%Y%m%d').strftime('%Y-%m-%d')
                period_end_formatted = datetime.datetime.strptime(period_end, '%Y%m%d').strftime('%Y-%m-%d')
                for stock in stock_list:
                    qlib_code = CustomIndexGenerator.tushare_code_to_qlib_code(stock)
                    if qlib_code:
                        f.write(f"{qlib_code}\t{period_start_formatted}\t{period_end_formatted}\n")
        print(f"指数成分股信息已保存到 {constituents_file}")

    def convert_constituents_to_qlib_format(self, constituents_csv, custom_index_code):
        """
        将指数成分股 CSV 文件转换为 qlib 需要的格式并保存。
        
        参数：
        - constituents_csv: 成分股 CSV 文件路径
        - custom_index_code: 自定义指数代码
        """
        df = pd.read_csv(constituents_csv)
        df['date'] = pd.to_datetime(df['date'])
        df['qlib_code'] = df['stock'].apply(CustomIndexGenerator.tushare_code_to_qlib_code)
        df = df.dropna(subset=['qlib_code'])
        grouped = df.groupby('date')['qlib_code'].apply(lambda codes: ','.join(codes)).reset_index()
        output_file = os.path.join(self.qlib_data_path, 'instruments', f"{custom_index_code}.txt")
        grouped.to_csv(output_file, sep='\t', index=False, header=False, date_format='%Y-%m-%d')
        print(f"指数成分股数据已保存到 {output_file}")


def main(start_date='20080101',
         end_date=None,
         industries=None,
         exclude_st=False,
         exclude_gem=False,
         exclude_star=False,
         exclude_bse=False,
         positive_pe=False,
         positive_growth=False,
         operate_profit_growth_ratio=0,
         consecutive_growth_quarters=1,
         index_code=None,
         market_cap_method=None,
         n=50,
         start=0,
         custom_index_name='自定义指数',
         qlib_data_path='qlib_bin',
         **kwargs):
    """
    使用 Fire 库启动的主函数。
    
    Args:
        start_date: 开始日期，默认'20080101'
        end_date: 结束日期，默认为当前日期
        industries: 行业列表，可以是逗号分隔的字符串
        exclude_st: 是否排除ST股票
        exclude_gem: 是否排除创业板
        exclude_star: 是否排除科创板
        exclude_bse: 是否排除北交所
        positive_pe: 是否要求PE为正
        positive_growth: 是否要求业绩增长
        operate_profit_growth_ratio: 营业利润增长比例要求
        consecutive_growth_quarters: 要求连续增长的季度数
        index_code: 指数代码
        market_cap_method: 市值过滤方法
        n: 选取的股票数量
        start: 起始位置
        custom_index_code: 自定义指数代码
        custom_index_name: 自定义指数名称
        qlib_data_path: qlib数据路径
        output_dir: 输出目录
        **kwargs: 其他参数
    """
    end_date = end_date or datetime.datetime.now().strftime('%Y%m%d')
    init_logger()
    init_qlib()
    start_date = str(start_date)
    end_date = str(end_date)

    # 解析 industries 参数
    if industries is not None and isinstance(industries, str):
        industries = industries.split(',')

    # 转换布尔字符串
    exclude_st = exclude_st in ['True', 'true', '1', True]
    exclude_gem = exclude_gem in ['True', 'true', '1', True]
    exclude_star = exclude_star in ['True', 'true', '1', True]
    exclude_bse = exclude_bse in ['True', 'true', '1', True]
    positive_pe = positive_pe in ['True', 'true', '1', True]
    positive_growth = positive_growth in ['True', 'true', '1', True]

    generator = CustomIndexGenerator(qlib_data_path=qlib_data_path)

    # 调用生成自定义指数方法，并通过 **kwargs 扩展更多参数的传递
    generator.generate_custom_index(
        start_date=start_date,
        e_date=end_date,
        industries=industries,
        exclude_st=exclude_st,
        exclude_gem=exclude_gem,
        exclude_star=exclude_star,
        exclude_bse=exclude_bse,
        positive_pe=positive_pe,
        positive_growth=positive_growth,
        operate_profit_growth_ratio=operate_profit_growth_ratio,
        consecutive_growth_quarters=consecutive_growth_quarters,
        index_code=index_code,
        market_cap_method=market_cap_method,
        n=n,
        start=start,
        custom_index_name=custom_index_name,
        **kwargs
    )


if __name__ == "__main__":
    fire.Fire(main)
