from functools import partial
import glob
import os.path
import shutil
import subprocess
from multiprocessing import Pool

import akshare as ak
import tqdm

from alpha_158 import prepare_alpha158_data, combined_with_a300
from alphabase import Alphas
from util import *
from util import get_intervals_from_config
import fire


def export_to_csv(mcode, save_path):
    if not os.path.exists(save_path):
        os.mkdir(save_path)
    try:
        instruments = D.list_instruments(D.instruments(market=mcode))
    except ValueError:
        instruments = D.list_instruments(D.instruments(market='all'))
    for instrument in instruments:

        code = instrument.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
        df = D.features([instrument], ["$close", "$volume", "$high", "$low", "$open",
                                       "$adjclose", '$amount', '$change', '$factor', '$vwap'], freq="day")
        df.rename(columns={"$close": "close",
                           "$volume": "volume",
                           "$high": "high",
                           "$low": "low", '$open': "open", '$adjclose': "adjclose", "$amount": "amount",
                           '$change': "change", '$factor': "factor", '$vwap': "vwap"}, inplace=True)

        df.index = df.index.get_level_values(1)
        df.dropna(inplace=True)
        df.to_csv(f"{save_path}/{code}.csv")


def update_data(today):
    url = f'https://github.com/chenditc/investment_data/releases/download/{today}/qlib_bin.tar.gz'
    logging.getLogger().info(f'download data from {url}')
    for filename in glob.glob('qlib_bin.tar.*'):
        os.remove(filename)
    subprocess.call(f'aria2c --file-allocation=none {url} --console-log-level=info', shell=True)
    if os.path.exists('./qlib_bin'):
        shutil.rmtree('./qlib_bin')
    subprocess.call('tar -zxvf ./qlib_bin.tar.gz', shell=True)


def export_to_dest_dir():
    csi_code = configs.convert_to_csi_code(config_info['market'])
    export_to_csv(csi_code, f"./{config_info['market']}_daily")


def download_index_data(code, save_name, last_day=None):
    if code.startswith('000'):
        prefix = 'sh'
    else:
        prefix = 'sz'
    df = ak.stock_zh_index_daily_tx(symbol=f'{prefix}{code}')
    df.index = pd.to_datetime(df['date'])
    df.drop(columns=['date'], inplace=True)
    df.rename(columns={'amount': 'volume'}, inplace=True)
    if last_day is not None:
        df = df[df.index <= pd.to_datetime(last_day)]
    df.to_csv(f'{save_name}.csv')


def alpha_generator_with_melt(alpha, alpha_class, factorname):
    factor = getattr(alpha_class, factorname)
    ret = factor(alpha)
    ret = ret.reset_index().melt(id_vars='date', var_name='sec_id', value_name=factorname)
    ret.to_pickle(f'feature/{factorname}.pkl')
    del ret

    return f"feature/{factorname} processed"


def update_progress_and_log(return_msg):
    pbar.set_postfix_str(return_msg)
    pbar.update(1)


def generate_alpha_300(start=None, end=None, prefix=''):
    df_all, stock_data = Alphas.get_stocks_data_by_date(end, start)
    logging.getLogger().info('total stocks code %d', len(df_all['sec_id'].unique()))

    if not os.path.exists('feature'):
        os.mkdir('feature')

    feature_names = get_feature_names('a300', config_info['market'], 10, MAX_FEATURE_COUNT)
    generate_all_features = config_info['generate_all_features']

    alpha_class_names = config_info['alpha_class'].split(',')
    alpha_classes = []
    
    for class_name in alpha_class_names:
        class_name = class_name.strip()
        try:
            # 分割模块名和类名
            module_name, class_name = class_name.rsplit('.', 1)
            # 动态导入模块
            module = __import__(module_name, fromlist=[class_name])
            # 获取类
            alpha_class = getattr(module, class_name)
            alpha_classes.append(alpha_class)
        except (ImportError, AttributeError) as e:
            logging.getLogger().warning(f"Failed to import alpha class: {class_name}, error: {e}")

    factor_list = []
    feature_list = []

    for alpha_class in alpha_classes:
        alpha_instance = alpha_class(stock_data)
        methods = alpha_class.get_alpha_methods(alpha_instance)
        for m in methods:
            if generate_all_features or (m in feature_names):
                if not os.path.exists(f'feature/{m}.pkl'):
                    factor_list.append((alpha_instance, alpha_class, m))
                feature_list.append(m)

    global pbar
    pbar = tqdm.tqdm(total=len(factor_list), desc="Generating alpha features")
    pool = Pool(processes=20)

    for args_ in factor_list:
        pool.apply_async(
            alpha_generator_with_melt,
            args=args_,
            callback=update_progress_and_log
        )

    pool.close()
    pool.join()
    pbar.close()

    del pool

    instruments = get_instrument_list()
    df_all = df_all[df_all['sec_id'].isin(instruments)]

    factors = None
    for feature_file in tqdm.tqdm(feature_list, desc='merge features'):
        factor_df = pd.read_pickle(f'feature/{feature_file}.pkl')
        if factors is None:
            factors = factor_df
            factors = factors[factors['sec_id'].isin(instruments)]
        else:
            factors = factors.merge(factor_df, on=['date', 'sec_id'], how='inner')
            del factor_df

    factors = factors[factors['sec_id'].isin(instruments)]
    factors['date'] = pd.to_datetime(factors['date'])
    factors.set_index(['date', 'sec_id'], inplace=True)
    df_all.set_index(['date', 'sec_id'], inplace=True)
    df_all.rename(columns={'change': 'return'}, inplace=True)
    factors.merge(df_all, left_index=True, right_index=True, how='inner').to_pickle(
        f'{prefix}a300_{config_info["market"]}.pkl')


def prepare_data(last_day, start_day=None):
    if start_day is None:
        start_day = last_day - datetime.timedelta(400)
    if config_info['download_data']:
        update_data(last_day.strftime("%Y-%m-%d"))
        download_index_data(config_info['market'], config_info['market'], last_day)
    print(f'prepare data from {start_day} to {last_day}')

    if config_info['update_data'] and config_info['download_data']:
        export_to_dest_dir()
        config_info['download_data'] = False

    prefix = 'daily_'
    last_day = last_day.strftime("%Y%m%d")
    start_day = start_day.strftime("%Y%m%d")

    if config_info['update_data']:
        generate_alpha_300(start_day, last_day, prefix)


def get_last_time_for_update():
    cur_time = datetime.datetime.now()
    today = datetime.date.today()
    if cur_time.hour < 18:
        today -= datetime.timedelta(days=1)
    if today.weekday() == 5:
        today = today - datetime.timedelta(1)
    elif today.weekday() == 6:
        today = today - datetime.timedelta(2)
    return today


def combine_refined_data_from_intervals():
    refined_data = pd.DataFrame()
    intervals = get_intervals_from_config()
    for start_date, end_date in intervals:
        suffix = start_date.strftime("%Y%m%d")
        filename = f'.refined_daily_a300_{config_info["market"]}_{suffix}.pkl'
        if os.path.exists(filename):
            data = pd.read_pickle(filename)
            refined_data = pd.concat([refined_data, data], axis=0)
    refined_data.to_pickle(f'refined_a300_{config_info["market"]}.pkl')
    del refined_data

def update_alpha_feature_for(file, alpha_class, alphas):
    """
    处理单个 refined 文件，计算各个 alpha 特征并更新保存文件。
    优化思路：将 df 一次转换为 MultiIndex 格式（以 'date' 和 'sec_id' 为索引），
    然后在每个 alpha 方法的更新过程中直接对该索引化 DataFrame 进行更新，
    避免在循环中频繁调用 set_index/reset_index 操作。
    """
    configs.init_config()
    try:
        # 加载当前文件
        df_indexed = pd.read_pickle(file)
        basename = os.path.basename(file)
        # 从文件名解析起始日期（假定格式为 refined_daily_a300_{market}_{start_date}.pkl）
        try:
            start_date_str = basename.split('_')[-1].split('.')[0]
            start_date = pd.to_datetime(start_date_str, format='%Y%m%d')
        except Exception as e:
            logging.getLogger().warning(f"解析文件 {basename} 日期失败：{e}")
            return None

        # 以当前数据的最大日期作为 end_date
        end_date = pd.to_datetime(df_indexed.index.get_level_values('date').max())
        # 为保证完整数据，向前扩展800天（与 prepare_data_for_all_year 保持一致）
        full_start_date = start_date - datetime.timedelta(days=800)

        # 获取完整股票数据，并用其初始化 alpha_class 实例
        try:
            _, stock_data = Alphas.get_stocks_data_by_date(end_date, full_start_date)
        except Exception as e:
            logging.getLogger().warning(f"获取完整股票数据失败：{e}")
            return None
        alpha_instance = alpha_class(stock_data)

        # 针对每个 alpha 方法，计算对应特征并更新 df_indexed
        for alpha_method in alphas:
            if not hasattr(alpha_class, alpha_method):
                logging.getLogger().warning(f"{alpha_class.__name__} 没有 {alpha_method} 方法，跳过。")
                continue
            try:
                method_func = getattr(alpha_class, alpha_method)
                # 调用方法计算特征，假定返回的 DataFrame 行索引为日期，列为 sec_id
                feature_df = method_func(alpha_instance)
                # 转换为长格式（注意保证 'date' 列存在）
                feature_df = feature_df.reset_index().melt(id_vars='date', var_name='sec_id', value_name=alpha_method)
                if 'date' not in feature_df.columns:
                    feature_df.rename(columns={'index': 'date'}, inplace=True)
                # 将特征 DataFrame 转换为 MultiIndex 格式
                ft_index = feature_df.set_index(['date', 'sec_id'])
                df_indexed.drop(columns=[alpha_method], inplace=True, errors='ignore')
                df_indexed = df_indexed.merge(ft_index, left_index=True, right_index=True, how='left')
            except Exception as e:
                logging.getLogger().warning(f"计算 {alpha_method} 时在文件 {file} 出错：{e}")

        df_indexed.to_pickle(file)
        return file
    except Exception as e:
        logging.getLogger().warning(f"处理文件 {file} 时发生异常: {e}")
        return None

def update_alpha_features_for_all_year(class_name, alphas):
    """
    针对每个由 prepare_data_for_all_year 生成的 refined 文件，
    并行更新文件中的 alpha 特征。
    参数：
        class_name: 包含 alpha 方法的类的全名（例如 "module.ClassName"）。
        alphas (list): 需要计算的 alpha 方法名称列表。
    """
    module_name, class_name = class_name.rsplit('.', 1)
    module = __import__(module_name, fromlist=[class_name])
    alpha_class = getattr(module, class_name)
    pattern = f".refined_daily_a300_{config_info['market']}_*.pkl"
    refined_files = glob.glob(pattern)
    if not refined_files:
        logging.getLogger().info(f"No refined files matching {pattern} were found.")
        return

    from multiprocessing import Pool
    from tqdm import tqdm
    pool = Pool(processes=20)

    # 使用 functools.partial 替代 lambda，使得传递给进程池的函数是顶层函数
    process_func = partial(update_alpha_feature_for, alpha_class=alpha_class, alphas=alphas)
    for _ in tqdm(pool.imap_unordered(process_func, refined_files),
                  total=len(refined_files), desc="Updating alpha features in files"):
        pass
    pool.close()
    pool.join()

    combine_refined_data_from_intervals()

def prepare_data_for_all_year(start_year=2008, end_date=None):
    # 保留start_year参数的意义，只处理start_year到end_date之间的数据
    if config_info['download_data']:
        if end_date is None or datetime.date.today() == end_date:
            now = get_last_time_for_update()
        else:
            now = end_date
    else:
        now = get_last_time() if end_date is None else end_date

    if end_date is None:
        end_date = now

    intervals = get_intervals_from_config()
    filtered_intervals = []
    for (s_date, e_date) in intervals:
        if s_date.year >= start_year and e_date <= end_date:
            filtered_intervals.append((s_date, e_date))
    filtered_intervals.reverse()
    for (start_date, end_date_int) in filtered_intervals:
        dst_file = f'.refined_daily_a300_{config_info["market"]}_{start_date.strftime("%Y%m%d")}.pkl'
        if os.path.exists(dst_file):
            continue
        reset_env()
        config_info['generate_ins_date'] = (start_date.strftime("%Y-%m-%d"), end_date_int.strftime("%Y-%m-%d"))

        prepare_data(end_date_int, start_date - datetime.timedelta(days=800))
        src_file = f'daily_a300_{config_info["market"]}.pkl'
        if os.path.exists(src_file):
            df = pd.read_pickle(src_file)
            print(f'change date inside {start_date} and {end_date_int}')
            df = df[(df.index.get_level_values(0) >= pd.to_datetime(start_date)) & (
                    df.index.get_level_values(0) <= pd.to_datetime(end_date_int))]
            df.to_pickle(dst_file)
            os.remove(src_file)

    combine_refined_data_from_intervals()


def reset_env():
    shutil.rmtree('feature', ignore_errors=True)
    if os.path.exists(f'daily_a300_{config_info["market"]}.pkl'):
        os.remove(f'daily_a300_{config_info["market"]}.pkl')
    if os.path.exists(f'refined_daily_a300_{config_info["market"]}.pkl'):
        os.remove(f'refined_daily_a300_{config_info["market"]}.pkl')

def run_default():
    market_id = config_info['rep_market_id'] if config_info['rep_market_id'] is not None else config_info['market']
    if not market_id.startswith('90'):
        download_index_data(market_id, config_info['market'])
    csi_code = configs.convert_to_csi_code(config_info['market'])
    export_to_csv(csi_code, f"./{config_info['market']}_daily")

def update_alpha(class_name, alphas):
    # 如果alphas以逗号分隔，则转换为列表
    if isinstance(alphas, str):
        alphas_list = [a.strip() for a in alphas.split(',') if a.strip()]
    else:
        alphas_list = alphas
    update_alpha_features_for_all_year(class_name, alphas_list)
    if config_info['alpha_158']:
        date = datetime.date.today()
        prepare_alpha158_data(datetime.date(2008, 1, 1), date)
        combined_with_a300()

if __name__ == '__main__':
    init_qlib()
    fire.Fire({
        'default': run_default,
        'update_alpha': update_alpha,
    })