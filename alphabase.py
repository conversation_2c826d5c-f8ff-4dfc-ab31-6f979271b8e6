from pathos.multiprocessing import Pool

from util import *


def min_periods_window(window):
    return min(int(window / 2), 10)


class Alphas(object):
    def __init__(self, df_data):
        pass

    @classmethod
    def get_stocks_data(cls, start=None, end=None, bm_path=None, df_dir=None):
        if bm_path is None:
            bm_path = f'{config_info["market"]}.csv'
        if df_dir is None:
            df_dir = f'{config_info["market"]}_daily'

        df = pd.read_csv(bm_path, index_col=0)
        df.index = pd.to_datetime(df.index)
        if start is not None and end is not None:
            bm_data = df[(df.index >= start) & (df.index <= end)]
        else:
            bm_data = df
        bm_data = bm_data.rename(columns={
            "open": "benchmark_open",
            "close": "benchmark_close"})
        bm_data = bm_data.loc[:, ['benchmark_open', 'benchmark_close']]

        generate_ins_date = config_info['generate_ins_date']
        stock_filter = []
        if generate_ins_date is not None:
            stock_filter = get_instrument_list()

        list_all = []
        for stock_file in os.listdir(df_dir):
            if not stock_file.endswith('csv'):
                continue
            stock_code = stock_file.split('.')[0]
            if len(stock_filter) > 0 and stock_code not in stock_filter:
                continue
            df = pd.read_csv(f'{df_dir}/{stock_file}', index_col=0)
            df.index = pd.to_datetime(df.index)
            if start is not None and end is not None:
                df = df[(df.index >= start) & (df.index <= end)]
            df['sec_id'] = stock_code
            df = df.merge(bm_data, how='left', left_index=True, right_index=True)
            list_all.append(df)

        df_all = pd.concat(list_all)
        df_all = df_all[df_all['sec_id'].notnull()]
        df_all['date'] = df_all.index
        return df_all, df_all.pivot(index='date', columns='sec_id')

    @classmethod
    def get_alpha_methods(cls, self):
        return (list(filter(lambda m: (m.startswith("alpha") or m.startswith('gtja') or m.startswith('claude')) and callable(getattr(self, m)),
                            dir(self))))

    @classmethod
    def generate_alphas(cls, alpha_type, start=None, end=None, prefix=""):
        df_all, stock_data = cls.get_stocks_data_by_date(end, start)
        stock = cls(stock_data)
        pool = Pool(processes=32)
        methods = cls.get_alpha_methods(cls)
        rets = []
        for m in methods:
            factor = getattr(cls, m)
            rets.append((m, pool.apply_async(factor, args=(stock,))))

        pool.close()
        pool.join()
        factors = None
        for a_index, r in rets:
            factor_df = r.get()
            factor_df = factor_df.reset_index().melt(id_vars='date', var_name='sec_id', value_name=a_index)
            if factors is None:
                factors = factor_df
            else:
                factors = factors.merge(factor_df, on=['date', 'sec_id'], how='inner')

        factors.set_index(['date', 'sec_id'], inplace=True)
        df_all.set_index(['date', 'sec_id'], inplace=True)
        df_all.rename(columns={'change': 'return'}, inplace=True)
        factors.merge(df_all, left_index=True, right_index=True, how='inner').to_csv(
            f'{prefix}{alpha_type}_{config_info["market"]}.csv')

    @classmethod
    def get_stocks_data_by_date(cls, end, start, bm_path=None, df_dir=None):
        df_all, stock_data = cls.get_stocks_data(bm_path=bm_path, df_dir=df_dir)
        if start is not None and end is not None:
            df_all = df_all[(df_all['date'] >= start) & (df_all['date'] <= end)]
            stock_data = stock_data[(stock_data.index >= start) & (stock_data.index <= end)]
        return df_all, stock_data
