{"cells": [{"cell_type": "code", "metadata": {"is_executing": true}, "source": ["from backtrader_plotting import Bokeh\n", "from backtrader_plotting.schemes import Tradimo\n", "from util import *\n", "from stratergy_runner import run_serialized_stratergy\n", "\n", "os.ch<PERSON>('900010')\n", "init_logger()\n", "init_qlib()"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {"is_executing": true, "scrolled": true}, "source": ["def draw_monthly_returns(cerebro, stats):\n", "    import ipywidgets as widgets\n", "    from IPython.display import display\n", "    import matplotlib.pyplot as plt\n", "    import pandas as pd\n", "    import backtrader as bt\n", "\n", "    # 设置中文字体\n", "    plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP']\n", "    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())\n", "    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]\n", "\n", "    # 创建 DataFrame\n", "    df = pd.DataFrame({\n", "        'datetime': pd.to_datetime(dates),\n", "        'value': values\n", "    })\n", "\n", "    # 计算每日涨跌幅\n", "    df.set_index('datetime', inplace=True)\n", "    df['daily_returns'] = df['value'].pct_change() * 100\n", "\n", "    # 按月计算涨跌幅\n", "    monthly_first = df['value'].resample('M').first()\n", "    monthly_last = df['value'].resample('M').last()\n", "    monthly_returns = (monthly_last - monthly_first) / monthly_first * 100\n", "\n", "    # 创建月份选择下拉框\n", "    months = [d.strftime('%Y-%m') for d in monthly_returns.index]\n", "    month_selector = widgets.Dropdown(\n", "        options=months,\n", "        description='选择月份:',\n", "        style={'description_width': 'initial'}\n", "    )\n", "\n", "    # 创建输出区域\n", "    output = widgets.Output()\n", "\n", "    # 显示控件和输出区域\n", "    display(month_selector)\n", "    display(output)\n", "\n", "    def plot_returns(change=None):\n", "        with output:\n", "            output.clear_output(wait=True)\n", "\n", "            # 创建图表布局\n", "            fig = plt.figure(figsize=(15, 10))\n", "\n", "            # 月度总览图\n", "            ax1 = plt.subplot(211)\n", "            colors = ['green' if x >= 0 else 'red' for x in monthly_returns]\n", "            ax = monthly_returns.plot(kind='bar', color=colors, width=0.8)\n", "\n", "            # 添加数值标签\n", "            for i, v in enumerate(monthly_returns):\n", "                if v >= 0:\n", "                    va = 'bottom'\n", "                    y_offset = 0.5\n", "                else:\n", "                    va = 'top'\n", "                    y_offset = -0.5\n", "                ax.text(i, v + y_offset, f'{v:.1f}%', ha='center', va=va)\n", "\n", "            plt.title('每月收益率', fontsize=16)\n", "            plt.xlabel('月份', fontsize=12)\n", "            plt.ylabel('收益率 (%)', fontsize=12)\n", "            plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "            plt.axhline(y=0, color='black', linewidth=1.0, linestyle='--', alpha=0.6)\n", "            plt.xticks(range(len(monthly_returns)),\n", "                       [d.strftime('%Y-%m') for d in monthly_returns.index],\n", "                       rotation=45)\n", "\n", "            # 如果选择了月份，显示该月每日涨跌幅\n", "            if month_selector.value:\n", "                selected_month = pd.to_datetime(month_selector.value)\n", "                month_data = df[df.index.strftime('%Y-%m') == month_selector.value]\n", "\n", "                ax2 = plt.subplot(212)\n", "                colors = ['green' if x >= 0 else 'red' for x in month_data['daily_returns']]\n", "                month_data['daily_returns'].plot(kind='bar', color=colors, ax=ax2)\n", "\n", "                # 添加数值标签\n", "                for i, v in enumerate(month_data['daily_returns']):\n", "                    if v >= 0:\n", "                        va = 'bottom'\n", "                        y_offset = 0.2\n", "                    else:\n", "                        va = 'top'\n", "                        y_offset = -0.2\n", "                    ax2.text(i, v + y_offset, f'{v:.1f}%', ha='center', va=va, fontsize=8)\n", "\n", "                plt.title(f'{month_selector.value} 每日收益率')\n", "                plt.xlabel('日期')\n", "                plt.ylabel('收益率 (%)')\n", "                plt.xticks(range(len(month_data)),\n", "                           [d.strftime('%d') for d in month_data.index],\n", "                           rotation=45)\n", "                plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "                plt.axhline(y=0, color='black', linewidth=1.0, linestyle='--', alpha=0.6)\n", "\n", "            plt.tight_layout()\n", "            plt.show()\n", "\n", "    # 设置选择框变化时的回调函数\n", "    month_selector.observe(plot_returns, names='value')\n", "\n", "    # 初始显示\n", "    plot_returns()\n", "\n", "\n", "\n", "\n", "def draw_trader_for_train_year(train_year):\n", "    test_year = train_year + 1\n", "    logging.getLogger().info(\n", "        f'cp -f {config_info[\"alpha_type\"]}_{config_info[\"market\"]}_ic_{train_year}.csv {config_info[\"alpha_type\"]}_{config_info[\"market\"]}_ic.csv')\n", "    if os.path.exists(f'{config_info[\"alpha_type\"]}_{config_info[\"market\"]}_ic_{train_year}.csv'):\n", "        os.system(\n", "            f'cp -f {config_info[\"alpha_type\"]}_{config_info[\"market\"]}_ic_{train_year}.csv {config_info[\"alpha_type\"]}_{config_info[\"market\"]}_ic.csv')\n", "    config_info['model_name'] = config_info[\n", "        'fixed_model'] = f'{config_info[\"feature_count\"]}_sel_{config_info[\"presets\"][0]}_{config_info[\"train_type\"]}_{config_info[\"alpha_type\"]}_{config_info[\"period_n\"]}_ends_{train_year}_t{config_info[\"tuning_days\"]}'\n", "\n", "    now = get_last_time()\n", "    endtime = f'{test_year}1231' if now.year != test_year else now.strftime('%Y%m%d')\n", "    ret, stats, cerebro = run_serialized_stratergy(f'{test_year}0101', endtime, 250)\n", "    \n", "        \n", "    benchmark_file = f\"{config_info['market']}.csv\"\n", "    benchmark_df = pd.read_csv(benchmark_file, index_col=0, parse_dates=True)\n", "    benchmark_df = benchmark_df.loc[\n", "                   (benchmark_df.index >= f'{test_year}0101') & (benchmark_df.index <= f'{test_year}1231'), :]\n", "    benchmark_df['benchmark'] = benchmark_df['close'].pct_change()\n", "    benchmark_df['benchmark'] = benchmark_df['benchmark'].fillna(0)\n", "    increase = benchmark_df['benchmark'].apply(lambda x: 1 if x > 0 else 0)\n", "    print(increase.value_counts())\n", "    print(benchmark_df['benchmark'].describe())\n", "    benchmark_df['benchmark'].hist(bins=40)\n", "    print(benchmark_df['benchmark'].sum())\n", "\n", "    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())\n", "    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]\n", "    values = pd.Series(values, index=dates)\n", "    change = values.pct_change()\n", "    change = change.fillna(0)\n", "    print((change - benchmark_df['benchmark'] > 0).sum() / len(change))\n", "    change.hist(bins=40)\n", "    print(change.sum())\n", "    \n", "    for dt in cerebro.datas:\n", "        dt.plotinfo.plot = False\n", "    stats[0].trade_data = stats[0].p.trade_data = 0\n", "    bokeh = Bokeh(style='bar', plot_mode='single', scheme=Tradimo())\n", "    cerebro.plot(bokeh)\n", "    return cerebro, stats\n", "    \n", "    "], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {"scrolled": true}, "source": "cerebro, stats = draw_trader_for_train_year(2020)", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "draw_monthly_returns(cerebro, stats)", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 4}