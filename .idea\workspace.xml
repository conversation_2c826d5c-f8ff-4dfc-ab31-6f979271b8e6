<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ccbb4031-b7b8-4492-a56f-f66d07096bc4" name="更改" comment="支持随机回测功能，优化标签构建和权重计算逻辑">
      <change beforePath="$PROJECT_DIR$/configs.py" beforeDir="false" afterPath="$PROJECT_DIR$/configs.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/stratergy_runner.py" beforeDir="false" afterPath="$PROJECT_DIR$/stratergy_runner.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/util.py" beforeDir="false" afterPath="$PROJECT_DIR$/util.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Rebase.Settings">
    <option name="NEW_BASE" value="origin/mini" />
  </component>
  <component name="Git.Settings">
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="origin/fixed_alphas2" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="v2.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;GuoRay&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/GuoRay/ai_retry.git&quot;,
    &quot;accountId&quot;: &quot;34c68e5e-db7f-4891-b452-5ea6013566d2&quot;
  }
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2m3vV5AwyzBxuvveni2Z43iljYm" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.additional_data_fetcher.executor": "Debug",
    "Python.additional_data_model.executor": "Run",
    "Python.alpha_158.executor": "Debug",
    "Python.data_preparer.executor": "Debug",
    "Python.index_assessment.executor": "Debug",
    "Python.integrated_feature_generator.executor": "Debug",
    "Python.manual_index.executor": "Debug",
    "Python.market_regime_identifier.executor": "Debug",
    "Python.position_research (train).executor": "Debug",
    "Python.position_research.executor": "Debug",
    "Python.stratergy_runner.executor": "Debug",
    "Python.test.executor": "Debug",
    "Python.wencai-evaluate.executor": "Debug",
    "Python.wencai.executor": "Debug",
    "Python.wencai_fit.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.mappings": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.symlinks": "true",
    "WebServerToolWindowPanel.toolwindow.show.date": "false",
    "WebServerToolWindowPanel.toolwindow.show.permissions": "false",
    "WebServerToolWindowPanel.toolwindow.show.size": "false",
    "git-widget-placeholder": "fixed__alphas2",
    "last_opened_file_path": "D:/Python/ai",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "web.server",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Python\ai" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Python\ai\tools" />
      <recent name="D:\Python\ai" />
    </key>
  </component>
  <component name="RunManager" selected="Python.market_regime_identifier">
    <configuration name="additional_data_fetcher" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\900004" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/additional_data_fetcher.py" />
      <option name="PARAMETERS" value="dump_index_to_qlib L3 20050101 20250221 /home/<USER>/qlib_bin_today csi900004" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="additional_data_model" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\runtime" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/additional_data_model.py" />
      <option name="PARAMETERS" value="evaluate 2020" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="alpha_158" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\000301" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/alpha_158.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="data_preparer" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (145)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\900004" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/data_preparer.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="index_assessment" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="d:\ai\python\900004" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tools/index_assessment.py" />
      <option name="PARAMETERS" value="csi900004" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="integrated_feature_generator" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\900006" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/integrated_feature_generator.py" />
      <option name="PARAMETERS" value="--index_code=csi900006 --start_date=20080102 --end_date=20241227 --combine=true" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="manual_index" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\900380" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/manual_index.py" />
      <option name="PARAMETERS" value="--custom_index_name=&quot;csi900380&quot; --qlib_data_path=&quot;/home/<USER>/qlib_bin_today&quot; --index_code=&quot;000120.SH&quot;" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="market_regime_identifier" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\000905" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/market_regime_identifier.py" />
      <option name="PARAMETERS" value="train --market_file 000905.csv --start_date 2014-01-01 --end_date 2024-12-31 --n_components 4" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="position_research (train)" type="PythonConfigurationType" factoryName="Python">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\900001" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/position_research.py" />
      <option name="PARAMETERS" value="train 900001.csv model 2011-06-01 2021-12-31" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="position_research" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\900001" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/position_research.py" />
      <option name="PARAMETERS" value="test model 900001.csv 2022-01-04 2024-10-10 10000" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="stratergy_runner" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\000905" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/stratergy_runner.py" />
      <option name="PARAMETERS" value="-r --year 2024 --end_year 2024" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="d:\Python\ai\900011" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="wencai-evaluate" type="PythonConfigurationType" factoryName="Python">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\runtime" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/wencai.py" />
      <option name="PARAMETERS" value="evaluate_wencai_features --start 20230101 --end 20231231 --model_path cpuic6_364_sel_m_reg_a458_10_ends_2023_t0 --csv_path score.csv" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="wencai" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\runtime" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/wencai.py" />
      <option name="PARAMETERS" value="prepare_scores_by_model  --start 20240701 --end 20241231 --csv_path score.csv --model_path linear.2024p1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="wencai_fit" type="PythonConfigurationType" factoryName="Python">
      <module name="ai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="ai_retry (30)" />
      <option name="WORKING_DIRECTORY" value="D:\Python\ai\runtime" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/wencai.py" />
      <option name="PARAMETERS" value="fit_model_with_autogluon --start 20240101 --end 20240630 --csv_path score.csv --model_path linear.2024p1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.additional_data_fetcher" />
      <item itemvalue="Python.test" />
      <item itemvalue="Python.additional_data_model" />
      <item itemvalue="Python.data_preparer" />
      <item itemvalue="Python.alpha_158" />
      <item itemvalue="Python.integrated_feature_generator" />
      <item itemvalue="Python.manual_index" />
      <item itemvalue="Python.position_research (train)" />
      <item itemvalue="Python.position_research" />
      <item itemvalue="Python.stratergy_runner" />
      <item itemvalue="Python.market_regime_identifier" />
      <item itemvalue="Python.index_assessment" />
      <item itemvalue="Python.wencai-evaluate" />
      <item itemvalue="Python.wencai" />
      <item itemvalue="Python.wencai_fit" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ccbb4031-b7b8-4492-a56f-f66d07096bc4" name="更改" comment="" />
      <created>1726318096056</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1726318096056</updated>
      <workItem from="1726318098292" duration="85000" />
      <workItem from="1726318208004" duration="812000" />
      <workItem from="1726319078540" duration="270000" />
      <workItem from="1726320546627" duration="5076000" />
      <workItem from="1726369272614" duration="1527000" />
      <workItem from="1726371032706" duration="10495000" />
      <workItem from="1726666674673" duration="326000" />
      <workItem from="1726667014503" duration="112000" />
      <workItem from="1726667137356" duration="360000" />
      <workItem from="1726668501035" duration="7761000" />
      <workItem from="1726794251544" duration="2833000" />
      <workItem from="1726840904756" duration="13147000" />
      <workItem from="1727016142989" duration="1267000" />
      <workItem from="1727400221760" duration="24403000" />
      <workItem from="1727572449808" duration="3347000" />
      <workItem from="1727665311928" duration="127000" />
      <workItem from="1727665452410" duration="18197000" />
      <workItem from="1727837449249" duration="12303000" />
      <workItem from="1728260856286" duration="58000" />
      <workItem from="1728260928606" duration="2153000" />
      <workItem from="1728351617264" duration="56000" />
      <workItem from="1728388900231" duration="3789000" />
      <workItem from="1728435868356" duration="15752000" />
      <workItem from="1728462700106" duration="12000" />
      <workItem from="1728478241919" duration="6372000" />
      <workItem from="1728524134215" duration="15771000" />
      <workItem from="1728734420430" duration="868000" />
      <workItem from="1728780383007" duration="7749000" />
      <workItem from="1728870577174" duration="50847000" />
      <workItem from="1729125394840" duration="9918000" />
      <workItem from="1729148340767" duration="15375000" />
      <workItem from="1729215427548" duration="9633000" />
      <workItem from="1729297650191" duration="12737000" />
      <workItem from="1729395633401" duration="6690000" />
      <workItem from="1729435842746" duration="58000" />
      <workItem from="1729469760957" duration="3097000" />
      <workItem from="1729478631426" duration="102000" />
      <workItem from="1729644337274" duration="4427000" />
      <workItem from="1729685138438" duration="13043000" />
      <workItem from="1729735881789" duration="234000" />
      <workItem from="1729736444281" duration="52000" />
      <workItem from="1729736515787" duration="2923000" />
      <workItem from="1729909220234" duration="86000" />
      <workItem from="1729909330340" duration="174000" />
      <workItem from="1729934670936" duration="5195000" />
      <workItem from="1730075375905" duration="2019000" />
      <workItem from="1730113370218" duration="10611000" />
      <workItem from="1730163210971" duration="152000" />
      <workItem from="1730770644199" duration="898000" />
      <workItem from="1730813500191" duration="295000" />
      <workItem from="1730813831254" duration="1100000" />
      <workItem from="1731336532692" duration="1492000" />
      <workItem from="1733360055004" duration="1202000" />
      <workItem from="1734008907102" duration="1430000" />
      <workItem from="1734011979109" duration="928000" />
      <workItem from="1734788819175" duration="5374000" />
      <workItem from="1734794881309" duration="6677000" />
      <workItem from="1735006379837" duration="2640000" />
      <workItem from="1735123560369" duration="943000" />
      <workItem from="1735124524732" duration="3358000" />
      <workItem from="1735137011639" duration="2609000" />
      <workItem from="1735181961556" duration="1941000" />
      <workItem from="1735210609762" duration="3889000" />
      <workItem from="1735268702607" duration="1305000" />
      <workItem from="1735270072849" duration="863000" />
      <workItem from="1735285807800" duration="16376000" />
      <workItem from="1735442294873" duration="17536000" />
      <workItem from="1735527232556" duration="4758000" />
      <workItem from="1735608768510" duration="2778000" />
      <workItem from="1735617358325" duration="1047000" />
      <workItem from="1735658163659" duration="1208000" />
      <workItem from="1735698581605" duration="10228000" />
      <workItem from="1735801132099" duration="3059000" />
      <workItem from="1736134451836" duration="1277000" />
      <workItem from="1736147916417" duration="226000" />
      <workItem from="1736149333299" duration="4474000" />
      <workItem from="1736172684604" duration="1844000" />
      <workItem from="1736207644437" duration="1789000" />
      <workItem from="1736212235907" duration="1830000" />
      <workItem from="1736303275074" duration="1095000" />
      <workItem from="1736329612528" duration="4967000" />
      <workItem from="1736350066622" duration="251000" />
      <workItem from="1736471024679" duration="1020000" />
      <workItem from="1737370270979" duration="2903000" />
      <workItem from="1737425647997" duration="749000" />
      <workItem from="1737443862182" duration="57000" />
      <workItem from="1737511016002" duration="5179000" />
      <workItem from="1737554040061" duration="2106000" />
      <workItem from="1737592983796" duration="868000" />
      <workItem from="1737642756568" duration="808000" />
      <workItem from="1738595652063" duration="383000" />
      <workItem from="1738596108651" duration="249000" />
      <workItem from="1738729451538" duration="7642000" />
      <workItem from="1738941883210" duration="484000" />
      <workItem from="1738982590830" duration="3086000" />
      <workItem from="1738998576786" duration="4496000" />
      <workItem from="1739840606040" duration="1418000" />
      <workItem from="1739862845266" duration="2459000" />
      <workItem from="1739887936973" duration="976000" />
      <workItem from="1740049151678" duration="6200000" />
      <workItem from="1740098685376" duration="6479000" />
      <workItem from="1740112923833" duration="8577000" />
      <workItem from="1740130187370" duration="12045000" />
      <workItem from="1740195977212" duration="13489000" />
      <workItem from="1740267374556" duration="2437000" />
      <workItem from="1740315098726" duration="5911000" />
      <workItem from="1740369190631" duration="2358000" />
      <workItem from="1740525991942" duration="114000" />
      <workItem from="1742283303955" duration="902000" />
      <workItem from="1754282595288" duration="13407000" />
    </task>
    <task id="LOCAL-00001" summary="修复并优化多个模块的代码，包括alpha_158、configs、serialized_strategy、stock_recommender、strategy_runner和util.py。提升代码性能和稳定性。">
      <option name="closed" value="true" />
      <created>1726663572521</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1726663572521</updated>
    </task>
    <task id="LOCAL-00002" summary="修改了stratergy_runner.py文件。">
      <option name="closed" value="true" />
      <created>1726666490493</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1726666490493</updated>
    </task>
    <task id="LOCAL-00003" summary="修改了stratergy_runner.py文件。">
      <option name="closed" value="true" />
      <created>1726721690256</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1726721690256</updated>
    </task>
    <task id="LOCAL-00004" summary="更新配置和数据准备逻辑，优化策略执行和工具函数。调整了configs.py、data_preparer.py、stratergy_runner.py和util.py文件。">
      <option name="closed" value="true" />
      <created>1727525098729</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1727525098729</updated>
    </task>
    <task id="LOCAL-00005" summary="更新配置和数据准备逻辑，优化策略执行和工具函数。调整了configs.py、data_preparer.py、stratergy_runner.py和util.py文件。">
      <option name="closed" value="true" />
      <created>1727611947214</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1727611947214</updated>
    </task>
    <task id="LOCAL-00006" summary="更新多个模块以优化序列化策略和评估器功能。修正了工具函数并改进了程序运行逻辑。">
      <option name="closed" value="true" />
      <created>1727914357259</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1727914357259</updated>
    </task>
    <task id="LOCAL-00007" summary="更新多个模块以优化序列化策略和评估器功能。修正了工具函数并改进了程序运行逻辑。">
      <option name="closed" value="true" />
      <created>1728351660045</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1728351660045</updated>
    </task>
    <task id="LOCAL-00008" summary="更新多个模块以优化序列化策略和评估器功能。修正了工具函数并改进了程序运行逻辑。">
      <option name="closed" value="true" />
      <created>1728398889952</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1728398889952</updated>
    </task>
    <task id="LOCAL-00009" summary="更新多个模块以优化序列化策略和评估器功能。修正了工具函数并改进了程序运行逻辑。">
      <option name="closed" value="true" />
      <created>1728442749200</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1728442749200</updated>
    </task>
    <task id="LOCAL-00010" summary="更新多个模块以优化序列化策略和评估器功能。修正了工具函数并改进了程序运行逻辑。">
      <option name="closed" value="true" />
      <created>1728483363870</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1728483363870</updated>
    </task>
    <task id="LOCAL-00011" summary="wencai使用autogluon模拟；config_info支持env覆盖；支持各种指数；add_info保存更全的行业信息">
      <option name="closed" value="true" />
      <created>1728549781856</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1728549781856</updated>
    </task>
    <task id="LOCAL-00012" summary="修正additional_data获取，支持无限重试">
      <option name="closed" value="true" />
      <created>1728613860714</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1728613860714</updated>
    </task>
    <task id="LOCAL-00013" summary="修正additional_data获取，支持无限重试">
      <option name="closed" value="true" />
      <created>1728834701705</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1728834701705</updated>
    </task>
    <task id="LOCAL-00014" summary="修正additional_data获取，支持无限重试">
      <option name="closed" value="true" />
      <created>1728875118675</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1728875118675</updated>
    </task>
    <task id="LOCAL-00015" summary="支持加入add_features训练模型，用get_relative_returns来获取模型系列的增量收益；支持测试各alpha的ic，以及预测后的数据再用alpha来看ic">
      <option name="closed" value="true" />
      <created>1729131468834</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1729131468834</updated>
    </task>
    <task id="LOCAL-00016" summary="支持加入add_features训练模型，用get_relative_returns来获取模型系列的增量收益；支持测试各alpha的ic，以及预测后的数据再用alpha来看ic">
      <option name="closed" value="true" />
      <created>1729131611230</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1729131611230</updated>
    </task>
    <task id="LOCAL-00017" summary="支持加入add_features训练模型，用get_relative_returns来获取模型系列的增量收益；支持测试各alpha的ic，以及预测后的数据再用alpha来看ic">
      <option name="closed" value="true" />
      <created>1729137580332</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1729137580332</updated>
    </task>
    <task id="LOCAL-00018" summary="支持加入add_features训练模型，用get_relative_returns来获取模型系列的增量收益；支持测试各alpha的ic，以及预测后的数据再用alpha来看ic">
      <option name="closed" value="true" />
      <created>1729168684462</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1729168684462</updated>
    </task>
    <task id="LOCAL-00019" summary="支持加入add_features训练模型，用get_relative_returns来获取模型系列的增量收益；支持测试各alpha的ic，以及预测后的数据再用alpha来看ic">
      <option name="closed" value="true" />
      <created>1729177334882</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1729177334882</updated>
    </task>
    <task id="LOCAL-00020" summary="更新新的参数；关闭回测日志；额外一个alpha配合下的回测；追后一日的持仓和操作打日志">
      <option name="closed" value="true" />
      <created>1729347365410</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1729347365410</updated>
    </task>
    <task id="LOCAL-00021" summary="支持stratergy延后x天开始">
      <option name="closed" value="true" />
      <created>1729425544271</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1729425544271</updated>
    </task>
    <task id="LOCAL-00022" summary="支持stratergy延后x天开始">
      <option name="closed" value="true" />
      <created>1729426725355</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1729426725355</updated>
    </task>
    <task id="LOCAL-00023" summary="支持stratergy延后x天开始">
      <option name="closed" value="true" />
      <created>1729474758822</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1729474758823</updated>
    </task>
    <task id="LOCAL-00024" summary="支持选择某几年单独测试">
      <option name="closed" value="true" />
      <created>1729648250617</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1729648250617</updated>
    </task>
    <task id="LOCAL-00025" summary="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；">
      <option name="closed" value="true" />
      <created>1729736050015</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1729736050015</updated>
    </task>
    <task id="LOCAL-00026" summary="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；">
      <option name="closed" value="true" />
      <created>1729738840991</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1729738840991</updated>
    </task>
    <task id="LOCAL-00027" summary="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；">
      <option name="closed" value="true" />
      <created>1729945332739</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1729945332739</updated>
    </task>
    <task id="LOCAL-00028" summary="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；">
      <option name="closed" value="true" />
      <created>1729952193090</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1729952193090</updated>
    </task>
    <task id="LOCAL-00029" summary="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；">
      <option name="closed" value="true" />
      <created>1730163356615</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1730163356615</updated>
    </task>
    <task id="LOCAL-00030" summary="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；">
      <option name="closed" value="true" />
      <created>1731340055039</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1731340055039</updated>
    </task>
    <task id="LOCAL-00031" summary="测试">
      <option name="closed" value="true" />
      <created>1735124185968</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1735124185968</updated>
    </task>
    <task id="LOCAL-00032" summary="支持按selected_year进行回测，回测的结果的邮件通知包含当前最优值等">
      <option name="closed" value="true" />
      <created>1735130536846</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1735130536846</updated>
    </task>
    <task id="LOCAL-00033" summary="重构run_all.sh">
      <option name="closed" value="true" />
      <created>1735183899054</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1735183899054</updated>
    </task>
    <task id="LOCAL-00034" summary="重构run_all.sh">
      <option name="closed" value="true" />
      <created>1735221175359</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1735221175359</updated>
    </task>
    <task id="LOCAL-00035" summary="避免复制ic文件；支持enable_min_res设置具体的值作为门槛">
      <option name="closed" value="true" />
      <created>1735289185694</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1735289185694</updated>
    </task>
    <task id="LOCAL-00036" summary="支持增量式计算ic；修正manual_index处理业绩增长的bug; 支持将tushare获取的属性附加到特征文件；支持分析所有tushare指数；自动化启动alpha_select">
      <option name="closed" value="true" />
      <created>1735609065386</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1735609065386</updated>
    </task>
    <task id="LOCAL-00037" summary="支持增量式计算ic；修正manual_index处理业绩增长的bug; 支持将tushare获取的属性附加到特征文件；支持分析所有tushare指数；自动化启动alpha_select">
      <option name="closed" value="true" />
      <created>1735659623435</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1735659623435</updated>
    </task>
    <task id="LOCAL-00038" summary="支持增量式计算ic；修正manual_index处理业绩增长的bug; 支持将tushare获取的属性附加到特征文件；支持分析所有tushare指数；自动化启动alpha_select">
      <option name="closed" value="true" />
      <created>1735726246658</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1735726246658</updated>
    </task>
    <task id="LOCAL-00039" summary="新的alpha120">
      <option name="closed" value="true" />
      <created>1735806798872</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1735806798872</updated>
    </task>
    <task id="LOCAL-00040" summary="新的alpha120">
      <option name="closed" value="true" />
      <created>1736168473098</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1736168473098</updated>
    </task>
    <task id="LOCAL-00041" summary="新的alpha120">
      <option name="closed" value="true" />
      <created>1736177827827</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1736177827827</updated>
    </task>
    <task id="LOCAL-00042" summary="新的alpha120">
      <option name="closed" value="true" />
      <created>1736350132308</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1736350132308</updated>
    </task>
    <task id="LOCAL-00043" summary="新的alpha120">
      <option name="closed" value="true" />
      <created>1737555070585</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1737555070585</updated>
    </task>
    <task id="LOCAL-00044" summary="新的alpha120">
      <option name="closed" value="true" />
      <created>1737643557029</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1737643557029</updated>
    </task>
    <task id="LOCAL-00045" summary="支持sortinorate, 支持新的alpha">
      <option name="closed" value="true" />
      <created>1738942357599</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1738942357600</updated>
    </task>
    <task id="LOCAL-00046" summary="支持sortinorate, 支持新的alpha">
      <option name="closed" value="true" />
      <created>1739002347363</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1739002347364</updated>
    </task>
    <task id="LOCAL-00047" summary="支持sortinorate, 支持新的alpha">
      <option name="closed" value="true" />
      <created>1739007099473</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1739007099473</updated>
    </task>
    <task id="LOCAL-00048" summary="支持行业指数的模拟，支持qlib里的mask引用指数，比较pkl和显示单个模型在一段时间的ic表现等小工具">
      <option name="closed" value="true" />
      <created>1740369434961</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1740369434961</updated>
    </task>
    <task id="LOCAL-00049" summary="支持随机回测功能，优化标签构建和权重计算逻辑">
      <option name="closed" value="true" />
      <created>1742283767796</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1742283767796</updated>
    </task>
    <option name="localTasksCounter" value="50" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/mini" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="mini" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修复并优化多个模块的代码，包括alpha_158、configs、serialized_strategy、stock_recommender、strategy_runner和util.py。提升代码性能和稳定性。" />
    <MESSAGE value="修改了stratergy_runner.py文件。" />
    <MESSAGE value="更新配置和数据准备逻辑，优化策略执行和工具函数。调整了configs.py、data_preparer.py、stratergy_runner.py和util.py文件。" />
    <MESSAGE value="更新多个模块以优化序列化策略和评估器功能。修正了工具函数并改进了程序运行逻辑。" />
    <MESSAGE value="wencai使用autogluon模拟；config_info支持env覆盖；支持各种指数；add_info保存更全的行业信息" />
    <MESSAGE value="修正additional_data获取，支持无限重试" />
    <MESSAGE value="支持加入add_features训练模型，用get_relative_returns来获取模型系列的增量收益；支持测试各alpha的ic，以及预测后的数据再用alpha来看ic" />
    <MESSAGE value="更新新的参数；关闭回测日志；额外一个alpha配合下的回测；追后一日的持仓和操作打日志" />
    <MESSAGE value="支持stratergy延后x天开始" />
    <MESSAGE value="支持选择某几年单独测试" />
    <MESSAGE value="支持自定义指数及测试，目前使用csiall作为指数数据，只定义指数成分；" />
    <MESSAGE value="测试" />
    <MESSAGE value="支持按selected_year进行回测，回测的结果的邮件通知包含当前最优值等" />
    <MESSAGE value="重构run_all.sh" />
    <MESSAGE value="避免复制ic文件；支持enable_min_res设置具体的值作为门槛" />
    <MESSAGE value="支持增量式计算ic；修正manual_index处理业绩增长的bug; 支持将tushare获取的属性附加到特征文件；支持分析所有tushare指数；自动化启动alpha_select" />
    <MESSAGE value="新的alpha120" />
    <MESSAGE value="支持sortinorate, 支持新的alpha" />
    <MESSAGE value="支持行业指数的模拟，支持qlib里的mask引用指数，比较pkl和显示单个模型在一段时间的ic表现等小工具" />
    <MESSAGE value="支持随机回测功能，优化标签构建和权重计算逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="支持随机回测功能，优化标签构建和权重计算逻辑" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/integrated_feature_generator.py</url>
          <line>261</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/integrated_feature_generator.py</url>
          <line>256</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/integrated_feature_generator.py</url>
          <line>234</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/integrated_feature_generator.py</url>
          <line>240</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/integrated_feature_generator.py</url>
          <line>246</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/integrated_feature_generator.py</url>
          <line>250</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/manual_index.py</url>
          <line>631</line>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/tools/web_interface.py</url>
          <line>515</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data_preparer.py</url>
          <line>18</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/alpha_158.py</url>
          <line>56</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/util.py</url>
          <line>1290</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/serialized_stratergy.py</url>
          <line>471</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/util.py</url>
          <line>1324</line>
          <option name="timeStamp" value="59" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/tools/index_assessment.py</url>
          <line>70</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/tools/index_assessment.py</url>
          <line>40</line>
          <option name="timeStamp" value="68" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/util.py</url>
          <line>757</line>
          <option name="timeStamp" value="73" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/market_regime_identifier.py</url>
          <line>190</line>
          <option name="timeStamp" value="74" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="jupyter-line">
          <url>file://$PROJECT_DIR$/draw_predict_ic.ipynb</url>
          <line>19</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="__py_debug_temp_var_1494058016" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/ai$market_regime_identifier.coverage" NAME="market_regime_identifier 覆盖结果" MODIFIED="1754300007151" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\000905" />
    <SUITE FILE_PATH="coverage/ai$manual_index.coverage" NAME="manual_index 覆盖结果" MODIFIED="1735546162993" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\900380" />
    <SUITE FILE_PATH="coverage/ai$position_research.coverage" NAME="position_research 覆盖结果" MODIFIED="1730123852627" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\900001" />
    <SUITE FILE_PATH="coverage/ai$data_preparer.coverage" NAME="data_preparer 覆盖结果" MODIFIED="1740117201447" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\900004" />
    <SUITE FILE_PATH="coverage/ai$wencai_evaluate.coverage" NAME="wencai-evaluate 覆盖结果" MODIFIED="1729090067972" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\runtime" />
    <SUITE FILE_PATH="coverage/ai$test.coverage" NAME="test 覆盖结果" MODIFIED="1740135514225" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="d:\Python\ai\900011" />
    <SUITE FILE_PATH="coverage/ai$stratergy_runner.coverage" NAME="stratergy_runner 覆盖结果" MODIFIED="1754293575056" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\000905" />
    <SUITE FILE_PATH="coverage/ai$index_assessment.coverage" NAME="index_assessment 覆盖结果" MODIFIED="1740203462878" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="d:\ai\python\900004" />
    <SUITE FILE_PATH="coverage/ai$wencai.coverage" NAME="wencai 覆盖结果" MODIFIED="1727796854031" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\runtime" />
    <SUITE FILE_PATH="coverage/ai$position_research__train_.coverage" NAME="position_research (train) 覆盖结果" MODIFIED="1730119062148" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\900001" />
    <SUITE FILE_PATH="coverage/ai$wencai_fit.coverage" NAME="wencai_fit 覆盖结果" MODIFIED="1728442480300" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\runtime" />
    <SUITE FILE_PATH="coverage/ai$integrated_feature_generator.coverage" NAME="integrated_feature_generator 覆盖结果" MODIFIED="1735478393184" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\900006" />
    <SUITE FILE_PATH="coverage/ai$additional_data_fetcher.coverage" NAME="additional_data_fetcher 覆盖结果" MODIFIED="1740317246426" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\900004" />
    <SUITE FILE_PATH="coverage/ai$alpha_158.coverage" NAME="alpha_158 覆盖结果" MODIFIED="1740060992599" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\000301" />
    <SUITE FILE_PATH="coverage/ai$additional_data_model.coverage" NAME="additional_data_model 覆盖结果" MODIFIED="1728961883890" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\Python\ai\runtime" />
  </component>
</project>