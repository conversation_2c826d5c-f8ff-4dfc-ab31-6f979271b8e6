import os
import sys

import pandas as pd


def update_env_file(feature_count, year_values):
    """Update .env file with new feature_count and enable_min_res values"""
    # Read existing .env file
    env_path = os.path.join(os.getcwd(), '.env')
    if not os.path.exists(env_path):
        print(f"Error: .env file not found at {env_path}")
        return False
    
    with open(env_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Update feature_count, assist_feature_count and enable_min_res lines
    new_lines = []
    enable_min_res_updated = False
    
    for line in lines:
        if line.startswith('feature_count='):
            new_lines.append(f'feature_count={feature_count}\n')
        elif line.startswith('assist_feature_count='):
            new_lines.append(f'assist_feature_count={feature_count}\n')
        elif line.startswith('enable_min_res='):
            new_lines.append(f'enable_min_res={year_values}\n')
            enable_min_res_updated = True
        else:
            new_lines.append(line)
    
    # If enable_min_res wasn't found, add it at the end
    if not enable_min_res_updated:
        new_lines.append(f'enable_min_res={year_values}\n')
    
    # Write back to .env file
    with open(env_path, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    return True

def process_csv(csv_path):
    """Process CSV file and return feature_count and year-value mapping for max total row"""
    try:
        # Read CSV file
        df = pd.read_csv(csv_path)
        
        # Find row with maximum total
        max_total_row = df.loc[df['total'].idxmax()]
        
        # Get feature_count from that row
        feature_count = int(max_total_row['feature_count'])
        
        # Get year columns (columns that are pure numbers)
        year_cols = [col for col in df.columns if str(col).isdigit()]
        
        # Create year-value mapping from the max total row
        year_values = {int(year): float(max_total_row[year]) for year in year_cols}
        
        return feature_count, year_values
        
    except Exception as e:
        print(f"Error processing CSV file: {str(e)}")
        return None, None

def main():
    # Check command line arguments
    if len(sys.argv) != 2:
        print("Usage: python after_fc_select.py <csv_file>")
        sys.exit(1)
    
    csv_path = sys.argv[1]
    
    # Check if file exists
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found at {csv_path}")
        sys.exit(1)
    
    # Process CSV file
    feature_count, year_values = process_csv(csv_path)
    if feature_count is None or year_values is None:
        sys.exit(1)
    
    # Update .env file
    if update_env_file(feature_count, year_values):
        print(f"Successfully updated .env file with feature_count={feature_count}")
        print(f"and enable_min_res={year_values}")
    else:
        print("Failed to update .env file")
        sys.exit(1)

if __name__ == "__main__":
    main() 