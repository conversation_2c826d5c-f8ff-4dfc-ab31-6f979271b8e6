import os.path

import argparse
import json
import pickle
import random
import re
import requests
import shutil
import time
import tqdm
from bs4 import BeautifulSoup
from langchain.chains.summarize import load_summarize_chain
from langchain.prompts import PromptTemplate
from langchain.schema import Document, HumanMessage, BaseMessage
from langchain.text_splitter import RecursiveCharacterTextSplitter
import akshare as ak
from util import *

cookies = {
    'qgqp_b_id': '6d025aa7cd04060e287907ec83cb00f4',
    'st_si': '68937133813917',
    'st_sn': '3',
    'st_psi': '20230606112507781-117001356556-2524615859',
    'st_asi': 'delete',
    'st_pvi': '02564770962976',
    'st_sp': '2023-03-24%2014%3A16%3A00',
    'st_inirUrl': 'https%3A%2F%2Fdata.eastmoney.com%2F',
}

headers = {
    'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/113.0',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
    'Referer': 'https://guba.eastmoney.com/',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
}

sleep_time = 100
global summary_cache
summary_cache = {}


def stock_news_em(symbol, page=1) -> pd.DataFrame:
    url = "https://search-api-web.eastmoney.com/search/jsonp"
    params = {
        "cb": "jQuery3510875346244069884_1668256937995",
        "param": '{"uid":"",'
                 + f'"keyword":"{symbol}"'
                 + ',"type":["cmsArticleWebOld"],"client":"web","clientType":"web","clientVersion":"curr","param":{"cmsArticleWebOld":{"searchScope":"default","sort":"default","pageIndex":' + str(
            page) + ',"pageSize":100,"preTag":"<em>","postTag":"</em>"}}}',
        "_": "1668256937996",
    }
    r = requests.get(url, params=params)
    data_text = r.text
    data_json = json.loads(
        data_text.strip("jQuery3510875346244069884_1668256937995(")[:-1]
    )
    temp_df = pd.DataFrame(data_json["result"]["cmsArticleWebOld"])
    if len(temp_df) == 0:
        return temp_df
    temp_df.rename(
        columns={
            "date": "发布时间",
            "mediaName": "文章来源",
            "code": "-",
            "title": "新闻标题",
            "content": "新闻内容",
            "url": "新闻链接",
            "image": "-",
        },
        inplace=True,
    )
    temp_df["关键词"] = symbol
    temp_df = temp_df[
        [
            "关键词",
            "新闻标题",
            "新闻内容",
            "发布时间",
            "文章来源",
            "新闻链接",
        ]
    ]
    temp_df["新闻标题"] = (
        temp_df["新闻标题"]
        .str.replace(r"\(<em>", "", regex=True)
        .str.replace(r"</em>\)", "", regex=True)
    )
    temp_df["新闻标题"] = (
        temp_df["新闻标题"]
        .str.replace(r"<em>", "", regex=True)
        .str.replace(r"</em>", "", regex=True)
    )
    temp_df["新闻内容"] = (
        temp_df["新闻内容"]
        .str.replace(r"\(<em>", "", regex=True)
        .str.replace(r"</em>\)", "", regex=True)
    )
    temp_df["新闻内容"] = (
        temp_df["新闻内容"]
        .str.replace(r"<em>", "", regex=True)
        .str.replace(r"</em>", "", regex=True)
    )
    temp_df["新闻内容"] = temp_df["新闻内容"].str.replace(r"\u3000", "", regex=True)
    temp_df["新闻内容"] = temp_df["新闻内容"].str.replace(r"\r\n", " ", regex=True)
    time.sleep(0.5)
    return temp_df


def guba_comments_em(symbol, page=1) -> pd.DataFrame:
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5 (Ergänzendes Update)) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Safari/605.1.15',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Referer': 'https://so.eastmoney.com/tiezi/s?keyword=%E4%B8%89%E5%85%AD%E9%9B%B6',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'same-site',
    }

    params = {
        'cb': 'jQuery35103099559325529341_1686406739559',
        'param': '{"uid":"","keyword":"' + symbol + '","type":["gubaArticleWeb"],"client":"web","clientVersion":"curr","clientType":"web","param":{"gubaArticleWeb":{"pageSize":100,"pageIndex":' + str(
            page) + ',"postTag":"","preTag":"","sortOrder":2}}}',
        '_': '1686406739567',
    }

    r = requests.get('https://search-api-web.eastmoney.com/search/jsonp', params=params, cookies=cookies,
                     headers=headers)
    data_text = r.text
    data_json = json.loads(
        data_text.strip("jQuery35103099559325529341_1686406739559(")[:-1]
    )
    temp_df = pd.DataFrame(data_json["result"]["gubaArticleWeb"])
    if len(temp_df) == 0:
        return temp_df
    temp_df.rename(
        columns={
            "createTime": "发布时间",
            "title": "新闻标题",
            "content": "新闻内容",
        },
        inplace=True,
    )
    temp_df = temp_df[
        [
            "新闻标题",
            "新闻内容",
            "发布时间",
        ]
    ]
    temp_df["新闻标题"] = (
        temp_df["新闻标题"]
        .str.replace(r"\(<em>", "", regex=True)
        .str.replace(r"</em>\)", "", regex=True)
    )
    temp_df["新闻标题"] = (
        temp_df["新闻标题"]
        .str.replace(r"<em>", "", regex=True)
        .str.replace(r"</em>", "", regex=True)
    )
    temp_df["新闻内容"] = (
        temp_df["新闻内容"]
        .str.replace(r"\(<em>", "", regex=True)
        .str.replace(r"</em>\)", "", regex=True)
    )
    temp_df["新闻内容"] = (
        temp_df["新闻内容"]
        .str.replace(r"<em>", "", regex=True)
        .str.replace(r"</em>", "", regex=True)
    )
    temp_df["新闻内容"] = temp_df["新闻内容"].str.replace(r"\u3000", "", regex=True)
    temp_df["新闻内容"] = temp_df["新闻内容"].str.replace(r"\r\n", " ", regex=True)
    time.sleep(0.5)
    return temp_df


def get_content_of_comment_em(url_str):
    if url_str.startswith('//'):
        url = f'https:{url_str}'
    else:
        url = f'https://guba.eastmoney.com{url_str}'
    response = requests.get(url, cookies=cookies, headers=headers)
    bea = BeautifulSoup(response.text, 'html.parser')
    div = bea.find_all('div', class_="xeditor_content app_h5_article")
    if len(div) > 0:
        text = div[0].text
        return text
    div = bea.find_all('div', id="zw_body")
    if len(div) > 0:
        return div[0].text
    div = bea.find_all('div', class_="newstext")
    if len(div) > 0:
        return div[0].text
    div = bea.find_all('div', class_='xeditor_content cfh_web')
    if len(div) > 0:
        return div[0].text
    return None


def get_comments_list_em(code, start_date):
    last_date = datetime.datetime.now()
    cur_year = last_date.year
    ret = []
    should_stop = False
    page = 1
    url = f'https://guba.eastmoney.com/list,{code}.html'
    while not should_stop:
        response = requests.get(url, cookies=cookies, headers=headers)
        bea = BeautifulSoup(response.text, 'html.parser')
        tbody = bea.find_all('tbody', class_='listbody')
        if len(tbody) == 0:
            print(f'failed to load url f{url}, maybe the crawler is blocked')
            break
        trs = tbody[0].find_all('tr')
        should_stop = True
        for tr in tqdm.tqdm(trs, desc=f'loading page {page}, code {code}'):
            title = tr.find_all('div', class_='title')[0]
            href = title.find_all('a')[0].get('href')
            time_str = tr.find('div', class_='update').text
            date = datetime.datetime.strptime(time_str + f' {cur_year}', '%m-%d %H:%M %Y')
            if last_date.month == 1 and date.month == 12:
                cur_year -= 1
            if date > start_date:
                should_stop = False
                date = date.replace(year=cur_year)
                last_date = date
                content = get_content_of_comment_em(href)
                if content is not None:
                    ret.append((date, title.text, href, content))
            time.sleep((random.random() * 200 + sleep_time) / 1000)
        page += 1
        url = f'https://guba.eastmoney.com/list,{code}_{page}.html'
    ret = pd.DataFrame(ret, columns=['date', 'title', 'url', 'content'])
    return ret


def get_content_list_em(code, name, start_date, end_date, callback):
    news = callback(symbol=name)
    if len(news) == 0:
        print(f'no news for code {code}, maybe crawler is blocked, try again by using code')
        news = callback(symbol=code)
    if len(news) == 0:
        return news
    news['发布时间'] = pd.to_datetime(news['发布时间'], format='%Y-%m-%d %H:%M:%S')
    news = news[(news['发布时间'] > start_date) & (news['发布时间'] < end_date)]
    page = 2
    while True:
        new_page = callback(symbol=name, page=page)
        if len(new_page) == 0:
            break
        new_page['发布时间'] = pd.to_datetime(new_page['发布时间'], format='%Y-%m-%d %H:%M:%S')
        new_page = new_page[(new_page['发布时间'] > start_date)]
        if len(new_page) == 0:
            break
        new_page = new_page[(new_page['发布时间'] < end_date)]
        news = pd.concat([news, new_page])
        page += 1
    return news


def sumerize_contents(content, sumerize_prompt, combine_prompt):
    if len(content) == 0:
        return ""
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=10000,
        chunk_overlap=200
    )
    sentences = text_splitter.split_text(content)
    docs = [Document(page_content=t) for t in sentences]

    llm = create_llm()

    sumerize_prompt_template = f"""{sumerize_prompt}:

    {{text}}

    """
    sumerize_prompt_template = PromptTemplate(template=sumerize_prompt_template, input_variables=["text"])

    combine_prompt_template = f"""{combine_prompt}:


    {{text}}


    """
    combine_prompt_template = PromptTemplate(template=combine_prompt_template, input_variables=["text"])
    if len(docs) > 1:
        chain = load_summarize_chain(llm, chain_type="map_reduce", map_prompt=sumerize_prompt_template,
                                     combine_prompt=combine_prompt_template, verbose=True)
    else:
        chain = load_summarize_chain(llm, chain_type='stuff', prompt=sumerize_prompt_template, verbose=True)
    ret = chain.run(input_documents=docs, token_max=8000)
    print(ret)
    return ret


def get_stock_news_content(code, name, start_date, end_date):
    news = get_content_list_em(code, name, start_date, end_date, stock_news_em)
    if len(news) == 0:
        return ''
    news.to_csv(f'news/news_{code}_{end_date}.csv', index=False)
    is_valid_row = news['新闻内容'].apply(
        lambda x: len(re.findall(r'[\u4e00-\u9fa5]', x)) > len(re.findall(r'\d', x))
    )
    news = news[is_valid_row]
    news = news.drop_duplicates(subset=['新闻内容'])
    news['新闻内容'] = news['新闻内容'].str.strip()
    content_str = '\n\n\n'.join(news.loc[:, '新闻内容'].tolist())
    return content_str


def summarize_stock_news(code, name, start_date, end_date):
    content_str = get_stock_news_content(code, name, start_date, end_date)
    if len(content_str) == 0:
        return ''
    summarize_prompt = f'你是一个专业的股票基金投资经理，以下是股票{name}(代码：{code})近5日的新闻内容,每条新闻间隔2个及以上换行符，请总结并发掘其中有价值的信息，并以此来判断这些信息对这只股票未来10日走势的影响。你的回复中要包含新闻总共有多少条，其中有多少正面/负面/中性的信息，主要依据还是你从中获取的信息对股票价格的潜在影响，不要对你的判断做出不相关的解释，比如你的预测可能有偏差之类；不要说与判断判断走势或总结信息之外的无关的废话，比如股市有不确定性或投资者应综合考虑做好风险管理之类:'
    combine_prompt = f'你是一个专业的股票基金投资经理，以下几段信息分别是你对股票{name}(代码：{code})近5日的部分新闻做出的总结，每段总结以两个以上换行符结尾。请在之前的这几段总结基础之上，进一步发掘有价值的信息，并以此来判断这些信息对这只股票未来10日走势的影响。你的回复中要包含新闻总共有多少条，其中有多少正面/负面/中性的信息，主要依据还是你从中获取的信息对股票价格的潜在影响，不要对你的判断做出不相关的解释，比如你的预测可能有偏差之类；不要说与判断判断走势或总结信息之外的无关的废话，比如股市有不确定性或投资者应综合考虑做好风险管理之类:'
    ret = sumerize_contents(content_str, summarize_prompt, combine_prompt)
    return ret


def get_stock_score_by_news_summarize(content):
    pattern = r'评分[：:为是]+\s*(\d+)'
    match = re.search(pattern, content)
    if match:
        score = int(match.group(1))
        return score
    return None


def get_stock_comments_content(code, name, start_date, end_date):
    comments = get_content_list_em(code, name, start_date, end_date, guba_comments_em)
    comments.to_csv(f'comments/comments_{code}_{end_date}.csv', index=False)
    pattern = r'[\$][\u4e00-\u9fa5]+[\(][A-Z0-9]{8}[\)]?[\$]'
    comments = comments.drop_duplicates(subset=['新闻内容'])
    comments['新闻内容'] = comments['新闻内容'].str.strip()
    content_str = '\n\n\n'.join(comments.loc[:, '新闻内容'].tolist())
    content_str = re.sub(pattern, '', content_str)
    return content_str


def summarize_stock_comments(code, name, start_date, end_date):
    content_str = get_stock_comments_content(code, name, start_date, end_date)
    if len(content_str) == 0:
        return ""
    summarize_prompt = f'你是一个专业的股票基金投资经理，以下是股票{name}(代码：{code})近5日的网友评论,每条评论间隔2个及以上换行符，请总结并发掘其中有价值的信息以及网友普遍的情绪和判断，并以此来判断这些信息对这只股票未来10日走势的影响。你的回复中要包含这些评论总共有多少条，其中有多少正面/负面/中性的信息，主要依据还是你从中获取的信息对股票价格的潜在影响，不要对你的判断做出不相关的解释，比如你的预测可能有偏差之类；不要说与判断判断走势或总结信息之外的无关的废话，比如股市有不确定性或投资者应综合考虑做好风险管理之类:'
    combine_prompt = f'你是一个专业的股票基金投资经理，以下几段信息分别是你对股票{name}(代码：{code})近5日的网友部分评论做出的总结，每段总结以两个以上换行符结尾。请在之前的这几段总结基础之上，进一步总结并发掘其中有价值的信息以及网友普遍的情绪和判断，并以此来判断这些信息对这只股票未来10日走势的影响。你的回复中要包含这些评论总共有多少条，其中有多少正面/负面/中性的信息，主要依据还是你从中获取的信息对股票价格的潜在影响，不要对你的判断做出不相关的解释，比如你的预测可能有偏差之类；不要说与判断判断走势或总结信息之外的无关的废话，比如股市有不确定性或投资者应综合考虑做好风险管理之类:'
    ret = sumerize_contents(content_str, summarize_prompt, combine_prompt)
    return ret


def compare_stocks():
    with open('gpt_infos/today_summary.pkl', 'rb') as store_file:
        global summary_cache
        summary_cache = pickle.load(store_file)
    final_str = ""
    for code, v in summary_cache.items():
        comments = v['comments'] if v['comments'] else "无"
        news = v['news'] if v['news'] else "无"
        final_str = final_str + f"{code}评论总结：{comments}\n\n{code}新闻总结: {news}\n\n\n\n"

    llm = create_llm('deepseek-coder')
    summarize_prompt = """以下是你之前做出的股票信息的总结，请对比他们，根据未来走势预测给出你最推荐的四只股票，并分析原因,在原因中，要保留各只股票的新闻数以及正负面及中性比例、评论数以及正负面和中性的比例等信息,也要考虑具体利好的消息的利好程度和利空消息的利空程度，对股价潜在的影响程度等信息。要综合的列出这只股票最重要的信息，尤其是可以用数字量化的增长或变化等：
{summary}"""

    template = PromptTemplate(template=summarize_prompt, input_variables=["summary"])
    res: BaseMessage = llm([HumanMessage(
        content=template.format(summary=final_str))])
    print(final_str)
    print(res.content)
    return res.content, final_str


def get_stock_codes_from_str(text):
    pattern = r'(\d{6}(?:, ?\d{6})*)'
    match = re.search(pattern, text)
    if match:
        return re.findall(r'\d{6}', match.group(1))


def select_stock(codes, date):
    start_date = date - datetime.timedelta(days=5)
    final_str = ""
    for code in codes:
        comments = summarize_stock_comments(code, code, start_date, date)
        comments = comments if comments else "无"
        news = summarize_stock_news(code, code, start_date, date)
        news = news if news else "无"
        final_str = final_str + f"{code}评论总结：{comments}\n\n{code}新闻总结: {news}\n\n\n\n"

    llm = create_llm()
    summarize_prompt = """以下是你之前做出的股票信息的总结，请对比他们，根据未来走势预测给出你最推荐的三只股票，并分析原因,在你回复的第一行，只列出这三只股票的代码，以逗号(,)分割，从第二行做具体分析：
               {summary}"""

    template = PromptTemplate(template=summarize_prompt, input_variables=["summary"])
    res: BaseMessage = llm([HumanMessage(
        content=template.format(summary=final_str))])
    print(final_str)
    print(res.content)

    return get_stock_codes_from_str(res.content)


def generate_summarize(code, start_date):
    now = datetime.datetime.now()
    comments = summarize_stock_comments(code, code, start_date, now)
    news = summarize_stock_news(code, code, start_date, now)
    summary_cache[code] = {'comments': comments, 'news': news}
    with open('gpt_infos/today_summary.pkl', 'wb') as store_file:
        pickle.dump(summary_cache, store_file)


def generate_for_today():
    gpt_infos_dir = 'gpt_infos'
    if os.path.exists(gpt_infos_dir):
        mtime_dt = datetime.datetime.fromtimestamp(os.path.getmtime(gpt_infos_dir))
        if mtime_dt.date() == datetime.datetime.today():
            shutil.rmtree('gpt_infos', ignore_errors=True)
            os.mkdir(gpt_infos_dir)
    else:
        os.mkdir(gpt_infos_dir)
    now = datetime.datetime.now()
    end = now - datetime.timedelta(days=5)
    prepared_data = pd.read_csv(f'today_{config_info["market"]}.csv')
    prepared_data = prepared_data.iloc[:10]
    prepared_data['sec_id'] = prepared_data['sec_id'].apply(lambda x: str(x).zfill(6))
    global summary_cache
    summary_cache = {}
    for i in range(0, len(prepared_data)):
        code = prepared_data.iloc[i]['sec_id']
        name = prepared_data.iloc[i]['名称']
        comments = summarize_stock_comments(code, name, end, now)
        news = summarize_stock_news(code, name, end, now)
        summary_cache[code] = {'comments': comments, 'news': news}
    with open('gpt_infos/today_summary.pkl', 'wb') as store_file:
        pickle.dump(summary_cache, store_file)
    return compare_stocks()


def dump_stock_infos_for_index():
    stocks = ak.index_stock_cons_csindex(symbol=config_info['market'])
    start = datetime.datetime.now() - datetime.timedelta(days=5)
    end = datetime.datetime.now()
    if not os.path.exists('stock_infos'):
        os.mkdir('stock_infos')
    for i in tqdm.tqdm(range(0, len(stocks)), desc='dumping stock infos'):
        code = stocks.iloc[i]['成分券代码']
        name = stocks.iloc[i]['成分券名称']
        new_content = get_stock_news_content(code, name, start, end)
        comments_content = get_stock_comments_content(code, name, start, end)
        if len(new_content) > 0:
            with open(f'stock_infos/{code}_news.txt', 'w') as f:
                f.write(new_content)
        if len(comments_content) > 0:
            with open(f'stock_infos/{code}_comments.txt', 'w') as f:
                f.write(comments_content)
        if len(new_content) > 10000:
            print(f'stock news {code} length is {len(new_content)}')
        if len(comments_content) > 10000:
            print(f'stock comments {code} length is {len(comments_content)}')
        sleep_time = random.random() * 200 + 100
        time.sleep(sleep_time / 1000)


def get_stock_news_scores_for_index():
    stocks = ak.index_stock_cons_csindex(symbol=config_info['market'])
    day_str = datetime.datetime.now().strftime('%Y%m%d')
    file_name = f'index_news_scores_{day_str}.csv'
    if os.path.exists(file_name):
        results_df = pd.read_csv(file_name)
        scores = results_df.apply(lambda x: get_stock_score_by_news_summarize(x['sum']), axis=1)
        results_df['score'] = scores
        results_df['code'] = results_df['code'].apply(lambda x: str(x).zfill(6))
    else:
        results_df = pd.DataFrame(columns=['code', 'name', 'score', 'sum'])

    for i in range(0, len(stocks)):
        code = stocks.iloc[i]['成分券代码']
        name = stocks.iloc[i]['成分券名称']
        if not results_df[results_df['code'] == code].empty:
            continue
        news = summarize_stock_news(code, name, datetime.datetime.now() - datetime.timedelta(days=5),
                                    datetime.datetime.now())

        if news:
            scores = get_stock_score_by_news_summarize(news)
            scores = scores if scores is not None else -1
            new_row_df = pd.DataFrame([{'code': code, 'name': name, 'score': scores, 'sum': news}])
            results_df = pd.concat([results_df, new_row_df], ignore_index=True)
            results_df.to_csv(file_name, index=False)
    results_df = results_df.sort_values(by='score', ascending=False)
    for i in range(0, 3):
        print(
            f"{results_df.iloc[i]['code']}:{results_df.iloc[i]['name']}:{results_df.iloc[i]['score']}:{results_df.iloc[i]['sum']}")

    today = pd.read_csv(f'today_{config_info["market"]}.csv')
    today['sec_id'] = today['sec_id'].apply(lambda x: str(x).zfill(6))
    pd.merge(today, results_df, left_on='sec_id', right_on='code', how='left').to_csv('today_gpt.csv', index=False)
    notify_by_email_with_attachment('index gpt info', file_name)
    notify_by_email_with_attachment('gpt today', 'today_gpt.csv')


def news_scrawler_main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-g", "--generate", action="store_true", help="generate summary for today")
    parser.add_argument("-c", "--compare", action="store_true", help="compare stocks and summarize")
    parser.add_argument("-i", "--index", action="store_true", help="summarize index")
    parser.add_argument('-d', '--dump', action='store_true', help='dump stock infos for index')
    args = parser.parse_args()
    if args.dump:
        dump_stock_infos_for_index()
        return
    if args.generate:
        final_summary, summary = generate_for_today()
        notify_by_email('gpt summary', '\n'.join([summary, final_summary]), also_to_jammie=True)
    elif args.compare:
        final_summary, summary = compare_stocks()
        print(summary)
        print(final_summary)
    elif args.index:
        get_stock_news_scores_for_index()


if __name__ == '__main__':
    init_gpt()
    news_scrawler_main()
