from util import *


def evaluate_model(alpha_type, period_n, test_start_date, end_date, model_name):
    df = load_stock_data(alpha_type, config_info['market'])
    factor_names = get_feature_names(alpha_type, config_info['market'], period_n)
    test_data = prepare_data_for_model(df, factor_names, period_n, test_start_date,
                                       end_date)
    test_data.dropna(subset=['return'], inplace=True)
    predictor = load_model(model_name)
    logging.getLogger().info('---------predictor evaluation---------')
    logging.getLogger().info(predictor.evaluate(test_data, display=True, detailed_report=True))
    return df, test_data, predictor

def leaderboard_model(alpha_type, period_n, test_start_date, end_date, model_name):
    df, test_data, predictor = evaluate_model(alpha_type, period_n, test_start_date, end_date, model_name)
    predicted_data = predict_proba(predictor, test_data)
    backtrader_pred = prepare_bt_pred_data(predicted_data, df, 0)
    backtrader_pred.rename(columns={'return': 'predict_probe'}, inplace=True)
    backtrader_pred.set_index(['date', 'sec_id'], inplace=True)
    backtrader_pred = pd.merge(backtrader_pred, df[f'return_{period_n}D'], left_index=True, right_index=True)
    return backtrader_pred


def run_trade_backer(model_name, period_n, test_start_date, end_date, alpha_type, prefix=''):
    predictor = load_model(model_name)
    df = load_stock_data(alpha_type, config_info['market'], prefix)
    factor_names = get_feature_names(alpha_type, config_info['market'], period_n)
    test_data = prepare_data_for_model(df, factor_names, period_n, test_start_date,
                                       end_date)
    logging.getLogger().info('log evaluate data for tests')
    evaluate_datas = test_data[test_data['return'].notna()]
    logging.getLogger().info(predictor.evaluate(evaluate_datas))

    predicted_data = predict_proba(predictor, test_data)
    backtrader_pred = prepare_bt_pred_data(predicted_data, df, 0)

    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date)
    trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]
    cerebro.addstrategy(TopNStratergy, pred_data=backtrader_pred, topn=config_info['topn'],
                        period=period_n, min_return=config_info['min_return'],
                        max_down=0.1, min_hold=1000, printlog=True, trade_data=trade_data)
    prepare_cerebro(cerebro)
    stats = cerebro.run(optreturn=True)
    log_msg = f'rtb for {model_name} from {test_start_date} to {end_date}'
    logging.getLogger().info(log_msg)
    dump_stats_cerebro(cerebro, stats, log_msg)
    return cerebro, stats


def train_and_evaluate(alpha_type, period_n, train_end_date, test_start_date, end_date, model_name):
    train(alpha_type, period_n, train_end_date, model_name)
    cerebro, stats = run_trade_backer(model_name, period_n, test_start_date, end_date, alpha_type)
    return cerebro.broker.getvalue() / 10000000


def train_and_evaluate_s(train_end_date, prefix=''):
    return train_and_evaluate(config_info['alpha_type'], config_info['period_n'], train_end_date,
                              pd.to_datetime(train_end_date) + pd.DateOffset(days=1),
                              pd.to_datetime(train_end_date) + pd.DateOffset(days=365),
                              f'{prefix}f{config_info["feature_count"]}_{config_info["train_type"]}_{config_info["presets"][0]}_{config_info["alpha_type"]}_{config_info["period_n"]}_ends_{train_end_date}_t{config_info["tuning_days"]}')


def train(alpha_type, period_n, train_end_date, model_name, prefix='', feature_count=-1):
    df = load_stock_data(alpha_type, config_info['market'], prefix)
    factor_names = get_feature_names(alpha_type, config_info['market'], period_n, feature_count)
    end_time = pd.to_datetime(train_end_date)
    start_time = end_time - pd.DateOffset(years=config_info['train_year'])
    start_time = start_time.replace(month=1, day=1)
    predictor = train_model(df, factor_names, period_n, start_time,
                            train_end_date, model_name)
    logging.getLogger().info(predictor.leaderboard())
    return predictor
