import datetime
import os
import time

import fire
import numpy as np
import pandas as pd
import tushare as ts
from tqdm import tqdm

from configs import init_config

def init_tushare():
    """初始化tushare"""
    ts.set_token(os.environ['TUSHARE'])
    return ts.pro_api()

def get_index_list(pro):
    """获取所有指数列表"""
    indices = []
    
    # 上证指数
    df_sh = pro.index_basic(market='SSE')
    indices.append(df_sh)
    
    # 深证指数
    df_sz = pro.index_basic(market='SZSE')
    indices.append(df_sz)
    
    # 合并所有指数
    df_indices = pd.concat(indices)
    return df_indices

def get_index_daily(pro, ts_code, start_date, end_date):
    """获取指数的每日行情数据"""
    try:
        df = pro.index_daily(ts_code=ts_code, 
                           start_date=start_date,
                           end_date=end_date,
                           fields='ts_code,trade_date,close,vol,amount')
        time.sleep(0.6)  # Tushare API限制
        return df
    except Exception as e:
        print(f"获取{ts_code}数据失败: {str(e)}")
        return pd.DataFrame()

def calculate_metrics(daily_data):
    """计算指数的各项指标"""
    if daily_data.empty:
        return None
    
    # 按时间正序排列
    daily_data = daily_data.sort_values('trade_date')
    
    # 计算日收益率
    daily_data['daily_return'] = daily_data['close'].pct_change()
    
    # 计算年化波动率
    volatility = daily_data['daily_return'].std() * np.sqrt(252)
    
    # 计算总收益率
    total_return = (daily_data['close'].iloc[-1] / daily_data['close'].iloc[0]) - 1
    
    # 计算年化收益率
    years = len(daily_data) / 252
    annualized_return = (1 + total_return) ** (1/years) - 1
    
    # 计算夏普比率 (假设无风险利率为3%)
    risk_free_rate = 0.03
    sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility != 0 else 0
    
    # 计算最大回撤
    daily_data['cummax'] = daily_data['close'].cummax()
    daily_data['drawdown'] = (daily_data['cummax'] - daily_data['close']) / daily_data['cummax']
    max_drawdown = daily_data['drawdown'].max()
    
    return {
        'total_return': total_return,
        'annualized_return': annualized_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'last_update': datetime.datetime.now().strftime('%Y%m%d')
    }

def load_existing_results(output_file):
    """加载已有的分析结果"""
    if os.path.exists(output_file):
        try:
            df = pd.read_csv(output_file, encoding='utf-8-sig')
            # 将百分比字符串转回浮点数
            for col in ['total_return', 'annualized_return', 'volatility', 'max_drawdown']:
                if col in df.columns:
                    df[col] = df[col].str.rstrip('%').astype('float') / 100
            return df
        except Exception as e:
            print(f"读取已有结果文件失败: {str(e)}")
    return pd.DataFrame()

def save_results(df_results, output_file):
    """保存分析结果"""
    # 创建结果的副本以进行格式化
    df_output = df_results.copy()
    
    # 格式化百分比
    for col in ['total_return', 'annualized_return', 'volatility', 'max_drawdown']:
        if col in df_output.columns:
            df_output[col] = df_output[col].apply(lambda x: f"{x*100:.2f}%")
    
    # 保存结果
    df_output.to_csv(output_file, index=False, encoding='utf-8-sig')

def analyze_indices(output_file):
    """分析所有指数的表现并生成报告"""
    pro = init_tushare()
    
    # 加载已有结果
    existing_results = load_existing_results(output_file)
    
    # 获取指数列表
    indices = get_index_list(pro)
    
    # 设置时间范围（过去5年）
    end_date = datetime.datetime.now().strftime('%Y%m%d')
    start_date = (datetime.datetime.now() - datetime.timedelta(days=5*365)).strftime('%Y%m%d')
    
    # 存储结果
    results = []
    
    # 分析每个指数
    for _, index in tqdm(indices.iterrows(), total=len(indices)):
        ts_code = index['ts_code']
        
        # 检查是否需要更新
        if not existing_results.empty and ts_code in existing_results['ts_code'].values:
            existing_row = existing_results[existing_results['ts_code'] == ts_code].iloc[0]
            last_update = existing_row.get('last_update', '19700101')
            if last_update >= end_date:
                # 使用现有结果
                results.append(existing_row.to_dict())
                continue
        
        # 获取日线数据
        daily_data = get_index_daily(pro, ts_code, start_date, end_date)
        
        if daily_data.empty:
            continue
            
        # 计算指标
        metrics = calculate_metrics(daily_data)
        
        if metrics is None:
            continue
            
        # 添加指数基本信息
        result = {col: index[col] for col in index.index}  # 包含所有index_basic字段
        result.update(metrics)  # 添加计算的指标
        
        results.append(result)
        
        # 增量保存结果
        df_temp = pd.DataFrame(results)
        df_temp = df_temp.sort_values('annualized_return', ascending=False)
        save_results(df_temp, output_file)
    
    # 最终结果
    df_results = pd.DataFrame(results)
    df_results = df_results.sort_values('annualized_return', ascending=False)
    
    # 最后一次保存
    save_results(df_results, output_file)
    print(f"分析结果已保存到 {output_file}")
    
    return df_results

def main(output_file='index_analysis.csv'):
    """
    主函数
    Args:
        output_file: 输出文件路径，默认为'index_analysis.csv'
    """
    return analyze_indices(output_file)

if __name__ == "__main__":
    init_config()
    fire.Fire(main)
