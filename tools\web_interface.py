import datetime
import logging
import os
import time
from collections import defaultdict

import akshare as ak
import pandas as pd
from flask import Flask, render_template_string, jsonify, request, send_file

# 初始化日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 缓存数据
price_cache = {}
CACHE_EXPIRE_TIME = 300  # 5分钟缓存过期

def get_minute_data(stock_code, date_str):
    """使用akshare获取股票分钟数据"""
    cache_key = f"{stock_code}_{date_str}"
    current_time = time.time()
    
    if cache_key in price_cache and current_time - price_cache[cache_key]['timestamp'] < CACHE_EXPIRE_TIME:
        return price_cache[cache_key]['data']
    
    try:
        # 添加市场前缀
        if stock_code.startswith('6'):
            symbol = f"sh{stock_code}"
        else:
            symbol = f"sz{stock_code}"
            
        # 获取5分钟K线数据
        df = ak.stock_zh_a_minute(symbol=symbol, period='5')
        
        # 过滤今日的数据
        df['day'] = pd.to_datetime(df['day'])
        target_date = datetime.date.today()
        df = df[df['day'].dt.date == target_date]
        
        if not df.empty:
            # 获取日线数据中的昨收价
            daily_data = ak.stock_zh_a_daily(symbol=symbol)
            daily_data['date'] = pd.to_datetime(daily_data['date']).dt.date
            prev_day_data = daily_data[daily_data['date'] < target_date].iloc[-1]
            df['pre_close'] = prev_day_data['close']
            df['open'] = df.iloc[0]['open']
            # 只保留需要的列
            df = df[['day', 'open', 'close', 'pre_close']]
            
            price_cache[cache_key] = {
                'data': df,
                'timestamp': current_time
            }
            return df
            
        return None
    except Exception as e:
        logger.error(f"Error fetching data for {stock_code}: {str(e)}")
        return None

# HTML模板更新，添加stock页面
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据查看器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .container { padding-top: 20px; }
        .btn-back { margin-bottom: 20px; }
        #fileTable, #dataTable, #stockTable, #chartContainer { display: none; }
        #fileTable.show, #dataTable.show, #stockTable.show, #chartContainer.show { display: block; }
        #chartContainer { height: 400px; }
        .net-value { font-size: 1.2em; margin: 10px 0; }
        .positive { color: red; }
        .negative { color: green; }
        #progressContainer {
            display: none;
            margin: 20px 0;
        }
        .progress {
            height: 25px;
        }
        .progress-bar {
            line-height: 25px;
        }
        #currentStock {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
        #debugInfo {
            margin-top: 10px;
            font-size: 0.8em;
            color: #666;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-3">
            <div class="container-fluid">
                <div class="navbar-nav">
                    <a class="nav-link" href="/">CSV文件</a>
                    <a class="nav-link" href="/stock">股票净值</a>
                </div>
            </div>
        </nav>
        
        <button id="backToFileListBtn" class="btn btn-primary btn-back" style="display: none;" onclick="showFileList()">返回列表</button>
        
        <div id="progressContainer">
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" 
                     aria-valuenow="0" 
                     aria-valuemin="0" 
                     aria-valuemax="100" 
                     style="width: 0%">0%</div>
            </div>
            <div id="currentStock">准备获取数据...</div>
        </div>

        <div id="stockTable" class="table-responsive show">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>文件夹</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for folder in folders %}
                    <tr>
                        <td>{{ folder.name }}</td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="viewStockData('{{ folder.path }}', '{{ folder.date }}')">查看</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div id="chartContainer">
            <div class="net-value">当前净值变化: <span id="currentValue">0.00</span></div>
            <div id="valueChart"></div>
            <div id="debugInfo"></div>
        </div>
        
        {{ original_template|safe }}
    </div>

    <script>
        function showFileList() {
            $('#fileTable').addClass('show');
            $('#dataTable').removeClass('show');
            $('#stockTable').removeClass('show');
            $('#chartContainer').removeClass('show');
            $('#progressContainer').hide();
            $('#backToFileListBtn').hide();
        }

        function updateProgress(progress) {
            const progressBar = $('.progress-bar');
            const percentage = Math.round(progress.percentage);
            progressBar.css('width', percentage + '%')
                      .attr('aria-valuenow', percentage)
                      .text(percentage + '%');
            $('#currentStock').text(`正在处理: ${progress.current_stock} (${progress.current}/${progress.total})`);
        }

        function viewStockData(folderPath, date) {
            $('#stockTable').removeClass('show');
            $('#progressContainer').show();
            $('#backBtn').show();

            // 开始轮询进度
            const progressChecker = setInterval(() => {
                $.get('/check_progress', function(progress) {
                    if (progress.error) {
                        clearInterval(progressChecker);
                        alert(progress.error);
                        showStockList();
                        return;
                    }
                    updateProgress(progress);
                });
            }, 500);

            // 获取数据
            $.get('/stock_data', {folder_path: folderPath, date: date}, function(data) {
                clearInterval(progressChecker);
                $('#progressContainer').hide();

                if (data.error) {
                    alert(data.error);
                    showStockList();
                    return;
                }
                
                // 添加调试信息
                $('#debugInfo').text('收到数据: ' + JSON.stringify(data, null, 2));
                
                if (!Array.isArray(data) || data.length === 0) {
                    alert('没有获取到有效数据');
                    showStockList();
                    return;
                }
                
                const times = data.map(d => d.time);
                const values = data.map(d => d.value);
                
                // 验证数据
                if (!values || values.length === 0 || values[values.length - 1] === undefined) {
                    alert('数据格式不正确');
                    console.log('Times:', times);
                    console.log('Values:', values);
                    showStockList();
                    return;
                }
                
                const trace = {
                    x: times,
                    y: values,
                    type: 'scatter',
                    mode: 'lines',
                    name: '净值变化'
                };
                
                const layout = {
                    title: '投资组合涨跌幅走势',
                    xaxis: { 
                        title: '时间',
                        tickformat: '%H:%M'
                    },
                    yaxis: { 
                        title: '涨跌幅 (%)',
                        tickformat: '.2f',
                        ticksuffix: '%'
                    }
                };
                
                try {
                    Plotly.newPlot('valueChart', [trace], layout);
                    
                    const lastValue = values[values.length - 1];
                    if (typeof lastValue === 'number') {
                        $('#currentValue').text(lastValue.toFixed(2) + '%')
                            .removeClass('positive negative')
                            .addClass(lastValue >= 0 ? 'positive' : 'negative');
                    } else {
                        $('#currentValue').text('N/A');
                    }
                } catch (e) {
                    console.error('Error plotting data:', e);
                    alert('绘制图表时出错');
                }
                
                $('#chartContainer').addClass('show');
            }).fail(function(jqXHR, textStatus, errorThrown) {
                clearInterval(progressChecker);
                $('#progressContainer').hide();
                console.error('Ajax error:', textStatus, errorThrown);
                alert('获取数据失败: ' + textStatus);
                showStockList();
            });
        }
    </script>
</body>
</html>
"""

# 进度跟踪
progress_data = {
    'current': 0,
    'total': 0,
    'current_stock': '',
    'percentage': 0
}

@app.route('/check_progress')
def check_progress():
    return jsonify(progress_data)

def update_progress(current, total, current_stock):
    progress_data['current'] = current
    progress_data['total'] = total
    progress_data['current_stock'] = current_stock
    progress_data['percentage'] = (current / total * 100) if total > 0 else 0

def calculate_portfolio_value(folder_path, date_str):
    """计算投资组合的净值变化
    对于买入的股票，使用当前价格相对开盘价的涨跌幅
    对于持仓的股票，使用当前价格相对昨收价的涨跌幅
    最终返回所有股票涨跌幅的平均值
    """
    holding_file = os.path.join(folder_path, f"{date_str}_holding.txt")
    if not os.path.exists(holding_file):
        return None
    
    buys = []
    holdings = []
    current_section = None
    
    with open(holding_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line == 'Buys:':
                current_section = 'buys'
            elif line == 'Holdings:':
                current_section = 'holdings'
            elif line and current_section == 'buys':
                buys.append(line)
            elif line and current_section == 'holdings':
                holdings.append(line)
    
    # 用于存储每个时间点的涨跌幅
    values = defaultdict(list)
    timestamps = set()
    
    # 重置进度
    progress_data['current'] = 0
    progress_data['total'] = len(buys) + len(holdings)
    
    # 处理所有股票
    all_stocks = buys + holdings
    for i, stock in enumerate(all_stocks):
        update_progress(i + 1, len(all_stocks), stock)
        logger.info(f"处理第 {i+1}/{len(all_stocks)} 个股票: {stock}")
        df = get_minute_data(stock, date_str)
        if df is not None:
            for _, row in df.iterrows():
                if stock in buys:
                    # 买入股票用开盘价计算涨跌幅
                    change_rate = (float(row['close']) - float(row['open'])) / float(row['open']) * 100
                else:  # stock in holdings
                    # 持仓股票用昨收价计算涨跌幅
                    change_rate = (float(row['close']) - float(row['pre_close'])) / float(row['pre_close']) * 100
                values[row['day']].append(change_rate)
                timestamps.add(row['day'])
    
    # 计算完成后重置进度
    update_progress(len(all_stocks), len(all_stocks), "处理完成")
    
    # 计算平均涨跌幅
    result = []
    for timestamp in sorted(timestamps):
        if values[timestamp]:
            avg_change = sum(values[timestamp]) / len(values[timestamp])
            result.append({
                'time': timestamp,
                'value': round(avg_change, 4)  # 保留4位小数的百分比
            })
    
    return result

def find_stock_folders():
    folders = {}
    for root, dirs, files in os.walk('.'):  # 遍历当前目录
        for filename in files:
            if filename.endswith('_holding.txt'):
                date_str = filename.split('_')[0]
                try:
                    date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
                    if root not in folders or date > folders[root]['date_obj']:
                        folders[root] = {
                            'name': os.path.basename(root),
                            'path': root,
                            'date': date_str,
                            'date_obj': date
                        }
                except ValueError:
                    continue
    return list(folders.values())

@app.route('/stock')
def stock():
    folders = find_stock_folders()
    return render_template_string(HTML_TEMPLATE.replace('{{ original_template|safe }}', ''), 
                                folders=folders)

@app.route('/stock_data')
def stock_data():
    folder_path = request.args.get('folder_path')
    date = request.args.get('date')
    
    try:
        data = calculate_portfolio_value(folder_path, date)
        if data is None:
            return jsonify({'error': '无法找到或处理数据'})
        
        # 验证数据格式
        if not isinstance(data, list):
            return jsonify({'error': '数据格式错误'})
        
        # 确保每个数据点都有正确的格式
        for item in data:
            if not isinstance(item, dict) or 'time' not in item or 'value' not in item:
                return jsonify({'error': '数据点格式错误'})
            if not isinstance(item['value'], (int, float)):
                return jsonify({'error': '净值数据类型错误'})
        
        logger.info(f"返回数据: {data}")
        return jsonify(data)
    except Exception as e:
        logger.error(f"处理数据时出错: {str(e)}")
        return jsonify({'error': str(e)})

def find_csv_files():
    files = []
    for root, dirs, filenames in os.walk('.'):
        for filename in filenames:
            if filename.startswith('fc') and filename.endswith('.csv'):
                files.append({
                    'folder': os.path.basename(root) if root != '.' else '当前目录',
                    'filename': filename,
                    'path': os.path.join(root, filename)
                })
    return files

@app.route('/')
def index():
    files = find_csv_files()
    return render_template_string(HTML_TEMPLATE.replace('{{ original_template|safe }}', '''
        <table id="fileTable" class="table table-striped table-hover show">
            <thead>
                <tr>
                    <th>文件夹</th>
                    <th>文件名</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for file in files %}
                <tr>
                    <td>{{ file.folder }}</td>
                    <td>{{ file.filename }}</td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewFile('{{ file.path }}')">查看</button>
                        <button class="btn btn-success btn-sm" onclick="downloadFile('{{ file.path }}')">下载</button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <table id="dataTable" class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>列名</th>
                    <th>值</th>
                </tr>
            </thead>
            <tbody id="dataTableBody">
            </tbody>
        </table>

        <script>
            function showFileList() {
                $('#fileTable').addClass('show');
                $('#dataTable').removeClass('show');
                $('#stockTable').removeClass('show');
                $('#chartContainer').removeClass('show');
                $('#progressContainer').hide();
                $('#backToFileListBtn').hide();
            }

            function viewFile(filepath) {
                $.get('/view_file', {filepath: filepath}, function(data) {
                    $('#dataTableBody').empty();
                    for (let [key, value] of Object.entries(data)) {
                        $('#dataTableBody').append(`
                            <tr>
                                <td>${key}</td>
                                <td>${value}</td>
                            </tr>
                        `);
                    }
                    $('#fileTable').removeClass('show');
                    $('#dataTable').addClass('show');
                    $('#backBtn').show();
                });
            }

            function downloadFile(filepath) {
                window.location.href = '/download_file?filepath=' + encodeURIComponent(filepath);
            }
        </script>
    '''), files=files)

@app.route('/download_file')
def download_file():
    filepath = request.args.get('filepath')
    try:
        # 将相对路径转换为绝对路径
        abs_filepath = os.path.abspath(filepath)
        return send_file(abs_filepath, as_attachment=True)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/view_file')
def view_file():
    filepath = request.args.get('filepath')
    try:
        df = pd.read_csv(filepath)
        max_total_row = df.loc[df['total'].idxmax()]
        result_dict = {
            'min_name': df['name'].min(),
            'max_name': df['name'].max(),
        }
        result_dict.update(max_total_row.to_dict())
        result_dict = {k: v for k, v in result_dict.items() if pd.notna(v)}
        return jsonify(result_dict)
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
