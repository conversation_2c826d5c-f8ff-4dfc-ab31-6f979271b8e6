#!/bin/bash

py_exec="~/miniconda3/envs/ai_retry/bin/python"

# 获取参数中的目录列表
dirs=("$@")

# 检查是否提供了目录参数
if [ ${#dirs[@]} -eq 0 ]; then
    echo "用法: $0 目录1 目录2 目录3 ..."
    exit 1
fi

# 运行 trade_date_generator.py，传入目录参数
$py_exec trade_date_generator.py "${dirs[@]}"

# 遍历目录列表
for ((i=0; i<${#dirs[@]}; i++)); do
    dir="${dirs[$i]}"
    echo "正在处理目录: $dir"

    # 进入指定目录
    cd "$dir" || { echo "无法进入目录 $dir"; exit 1; }

    if [ $i -eq 0 ]; then
        rm -rf fc_res_selected.csv
        export consider_trade_date=1
    fi

    # 运行策略脚本
    $py_exec ../stratergy_runner.py -r --fc_res fc_res_selected

    # 如果有下一个目录，复制文件过去
    if [ $i -lt $((${#dirs[@]} - 1)) ]; then
        next_dir="../${dirs[$((i+1))]}"
        cp fc_res_selected.csv "$next_dir/"
    fi

    # 返回上一级目录
    cd - > /dev/null

done

# 定义CSV文件的路径（使用最后一个目录中的文件）
last_dir="${dirs[-1]}"
CSV_FILE="$last_dir/fc_res_selected.csv"

# 定义要处理的列名
columns=("total" "2024" "2023" "2022" "2021" "2020")

# 初始化一个关联数组来存储每个列的乘积，初始值为 1
declare -A products
for col in "${columns[@]}"; do
    products[$col]=1
done

# 使用 awk 读取 CSV 文件并进行计算
awk -F',' -v cols="${columns[*]}" '
BEGIN{
    # 将列名字符串拆分为数组
    split(cols, colNames, " ")
}
NR==1{
    # 处理标题行，建立列名到列号的映射
    for(i=1; i<=NF; i++){
        gsub(/"/, "", $i)   # 去除引号
        header[$i] = i
    }
    # 检查所需的列是否存在
    for(c in colNames){
        col = colNames[c]
        if(!(col in header)){
            print "错误：未找到列 " col > "/dev/stderr"
            exit 1
        }
    }
}
NR>1{
    # 对于每一行，处理指定的列
    for(c in colNames){
        col = colNames[c]
        idx = header[col]
        val = $idx
        gsub(/"/, "", val)
        gsub(/ /, "", val)
        if(val == ""){
            print "警告：在行 " NR " 的列 " col " 中值为空，已跳过。" > "/dev/stderr"
            next
        }
        # 打印列名和值，以供后续处理
        print col","val
    }
}
' "$CSV_FILE" | \
# 使用 awk 进行乘积计算
awk -F',' '
{
    col = $1
    val = $2 + 0  # 强制转换为数字
    if(val == 0){
        print "警告：在列 " col " 中发现值为 0，乘积将为 0。" > "/dev/stderr"
    }
    # 初始化乘积为 1
    if(!(col in product)){
        product[col] = 1
    }
    # 累乘
    product[col] *= val
}
END{
    for(col in product){
        printf("%s：%.10f\n", col, product[col])
    }
}
'

# 重置环境变量
export consider_trade_date=0
