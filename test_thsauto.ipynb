{"cells": [{"metadata": {}, "cell_type": "code", "source": "from thsauto import THS", "id": "8c5dc9de277b27eb", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["t = THS()\n", "t.connect('10.1.1.62:5555')"], "id": "f67cfc31db3880c", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "t.get_balance()", "id": "c97cee0e66d43827", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "t.get_positions()", "id": "f278b5a3d180dad9", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "t.get_orders()", "id": "9f21a12c86061537", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["confirm, prompt = t.buy(100, code='600000', debug=False)\n", "confirm, prompt"], "id": "155a9cc960c3f820", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["t.get_orders()\n", "confirm, prompt = t.cancel_single(t.order_at(0))\n", "confirm, prompt"], "id": "4c0c91ed37ded33d", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}