import argparse
import random
import re
import sys
import time

import akshare as ak
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain_community.llms.openai import AzureOpenAI
from langchain_core.prompts import PromptTemplate
from tqdm import tqdm

from news_scrawler import get_content_list_em, stock_news_em, \
    guba_comments_em
from util import *


class GPTScorer:
    days_weights = [1, 0.8, 0.7, 0.55, 0.45]
    min_weight = 0.2

    @staticmethod
    def instance():
        if not hasattr(GPTScorer, "_instance"):
            GPTScorer._instance = GPTScorer()
        return GPTScorer._instance

    def __init__(self):
        openai = AzureOpenAI(deployment_name="gpt-35-turbo-instruct", openai_api_version="2023-07-01-preview",
                             model_name='gpt-35-turbo-instruct', temperature=0,
                             max_tokens=5,
                             top_p=1,
                             frequency_penalty=0,
                             presence_penalty=0,
                             best_of=1,
                             stop=None)
        prompt_template = "对冒号后关于关于{code}的新闻进行关于这只股票的积极性打分，-5为最消极，5为最积极，如果不相干或者最中性，则为0：{content}\n\n得分："
        prompt = PromptTemplate(
            input_variables=["code", 'content'], template=prompt_template
        )
        self.news_llm = LLMChain(llm=openai, prompt=prompt)
        prompt_template = "对以下关于{code}的评论进行关于这只股票的积极性打分，-5为最消极，5为最积极，如果不相干或者最中性，则为0：{content}\n\n得分："
        prompt = PromptTemplate(
            input_variables=["code", 'content'], template=prompt_template
        )
        self.comments_llm = LLMChain(llm=openai, prompt=prompt)

    def get_weighted_score(self, code, name, row, llm):
        time = pd.to_datetime(row['发布时间'])
        days = (datetime.datetime.now() - time).days
        weight = GPTScorer.days_weights[days] if days < len(GPTScorer.days_weights) else GPTScorer.min_weight
        result = llm.predict(code=f'{name}(代码{code})', content=row['新闻内容'])
        result = result.strip()
        match = re.match(r'^-?\d+\.?\d*', result)
        if match:
            print(row['新闻内容'] + '\t\t' + match.group(0))
            return float(match.group(0)) * weight
        return 0

    def get_market_scores_table(self, market):
        stocks = ak.index_stock_cons_csindex(symbol=market)
        day_str = datetime.datetime.now().strftime('%Y%m%d')
        file_name = f'index_news_scores_{day_str}_{market}.csv'
        if os.path.exists(file_name):
            results_df = pd.read_csv(file_name)
            results_df['code'] = results_df['code'].apply(lambda x: str(x).zfill(6))
        else:
            results_df = pd.DataFrame(
                columns=['code', 'name', 'news_score', 'comments_score', 'news_count', 'comments_count'])

        for i in tqdm(range(0, len(stocks))):
            code = stocks.iloc[i]['成分券代码']
            if not results_df[results_df['code'] == code].empty:
                continue
            name = stocks.iloc[i]['成分券名称']
            score_line = self.get_score(code, name)
            results_df = pd.concat([results_df, score_line], ignore_index=True)
            results_df.to_csv(file_name, index=False)
            time.sleep(random.randint(1, 5))
        return results_df

    def get_score(self, code, name):
        news = get_content_list_em(code, name, datetime.datetime.now() - datetime.timedelta(days=5),
                                   datetime.datetime.now(), stock_news_em)
        news_score = 0
        if len(news) > 0:
            is_valid_row = news['新闻内容'].apply(
                lambda x: len(re.findall(r'[\u4e00-\u9fa5]', x)) > len(re.findall(r'\d', x))
            )
            news = news[is_valid_row]
            if len(news) > 0:
                news = news.drop_duplicates(subset=['新闻内容'])
                news['新闻内容'] = news['新闻内容'].str.strip()
                news_score = news.apply(lambda x: self.get_weighted_score(code, name, x, self.news_llm), axis=1)
                news_score = news_score.sum()

        comments = get_content_list_em(code, name, datetime.datetime.now() - datetime.timedelta(days=5),
                                       datetime.datetime.now(), guba_comments_em)
        if len(comments) > 0:
            pattern = r'[\$][\u4e00-\u9fa5]+[\(][A-Z0-9]{8}[\)]?[\$]'
            comments['新闻内容'] = comments['新闻内容'].str.strip()
            comments.apply(lambda x: re.sub(pattern, '', x['新闻内容']), axis=1)
            comments = comments.drop_duplicates(subset=['新闻内容'])
            comments_score = comments.apply(lambda x: self.get_weighted_score(code, name, x, self.comments_llm), axis=1)
            comments_score = comments_score.sum()
        else:
            comments_score = 0
        new_row_df = pd.DataFrame(
            [{'code': code, 'name': name, 'news_score': news_score, 'comments_score': comments_score,
              'news_count': len(news['新闻内容']), 'comments_count': len(comments['新闻内容'])}])
        return new_row_df


if __name__ == '__main__':
    init_gpt()
    parser = argparse.ArgumentParser()
    parser.add_argument('-m', '--market', type=str, help='market')
    parser.add_argument('-y', '--yesterday', action='store_true', help='yesterday')
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()
    market = args.market if args.market is not None else config_info['market']
    res = GPTScorer.instance().get_market_scores_table(market)
    res.set_index('code', inplace=True)
    today = pd.read_csv(f'today_{market}.csv')
    today['sec_id'] = today['sec_id'].apply(lambda x: str(x).zfill(6))
    today.set_index('sec_id', inplace=True)
    res = today.join(res, how='left')
    res.to_csv(f'today_{market}_with_news.csv')

    if args.yesterday:
        df = load_stock_data(config_info['alpha_type'], market)
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        df = df.loc[(df.index.get_level_values(0) == yesterday.strftime('%Y%m%d')), ['return_1D']]
        df.index = df.index.droplevel(0)
        df = df.join(res, how='left')
        df.to_csv(f'today_{market}_with_news.csv')

        for column in df.columns:
            if pd.api.types.is_numeric_dtype(df[column]):
                if column == 'return_1D':
                    continue
                correlation = df[column].corr(df['return_1D'])
                print(f"The correlation between 'return_1D' and '{column}' is {correlation}")
    notify_by_email_with_attachment('news_summary', f'today_{market}_with_news.csv')
