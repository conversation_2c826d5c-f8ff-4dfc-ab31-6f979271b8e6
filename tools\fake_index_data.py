import os
import shutil
from datetime import datetime

import fire
import pandas as pd
import qlib
from qlib.data import D

from dump_bin import DumpDataAll, DumpDataUpdate


def main(index_name, csv_name):
    index_name = str(index_name)
    csv_name = str(csv_name)
    qlib.init(provider_uri=os.path.expanduser('~/qlib_bin_today'))  # 根据您的数据路径进行调整

    # 设置起始和结束日期
    start_date = '2008-01-01'
    end_date = datetime.today().strftime('%Y-%m-%d')

    # 生成每个月的起始日期列表
    months = pd.date_range(start=start_date, end=end_date, freq='MS')

    # 初始资金
    initial_capital = 1000.0
    current_capital = initial_capital

    # 创建空的 DataFrame 来存储新指数的数据
    index_data = pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume'])

    last_prices = {}

    for month_start in months:
        month_start_date = month_start.strftime('%Y-%m-%d')
        # 获取本月的最后一天
        month_end_date = (month_start + pd.offsets.MonthEnd()).strftime('%Y-%m-%d')

        components = D.list_instruments(D.instruments(market=index_name),
                                        start_time=month_start_date, end_time=month_end_date)

        if not components:
            print(f"{month_start_date} 没有成分股数据。")
            continue

        # 获取本月的交易日历
        trading_days = qlib.data.D.calendar(start_time=month_start_date, end_time=month_end_date)
        if trading_days is None or len(trading_days) == 0:
            continue
        # 获取本月的第一个交易日
        first_day = trading_days[0]
        first_day_str = first_day.strftime('%Y-%m-%d')

        # 在每月的第一个交易日进行再平衡（等权重）
        # 获取开盘价
        features = ['$open']
        df_open = qlib.data.D.features(components, features, start_time=first_day_str, end_time=first_day_str)
        df_open = df_open.reset_index()
        df_open = df_open.rename(columns={'instrument': 'stock_code'})

        # 删除缺失数据
        df_open = df_open.dropna()
        num_stocks = len(df_open)
        if num_stocks == 0:
            print(f"{first_day_str} 没有可交易的股票。")
            continue

        # 计算每只股票的持股数量（等权重）
        equal_weight = current_capital / num_stocks
        holdings = {}
        for idx, row in df_open.iterrows():
            stock_code = row['stock_code']
            open_price = row['$open']
            if open_price > 0:
                shares = equal_weight / open_price
                holdings[stock_code] = shares
                last_prices[stock_code] = open_price
            else:
                print(f"{stock_code} 在 {first_day_str} 的开盘价无效。")

        # 遍历本月的每个交易日
        for day in trading_days:
            day_str = day.strftime('%Y-%m-%d')
            # 获取持仓股票的行情数据
            stock_list = list(holdings.keys())
            if not stock_list:
                continue
            features = ['$open', '$high', '$low', '$close', '$volume']
            df_day = qlib.data.D.features(stock_list, features, start_time=day_str, end_time=day_str)
            df_day = df_day.reset_index()
            df_day = df_day.rename(columns={'instrument': 'stock_code'})

            # 删除缺失数据
            df_day = df_day.dropna()
            valid_stocks = df_day['stock_code'].tolist()

            # 初始化每日指标
            total_open = total_high = total_low = total_close = total_volume = 0

            # 计算当日持仓的市值
            for idx, row in df_day.iterrows():
                stock_code = row['stock_code']
                shares = holdings.get(stock_code, 0)
                if shares == 0:
                    continue
                open_price = row['$open']
                high_price = row['$high']
                low_price = row['$low']
                close_price = row['$close']
                volume = row['$volume']
                total_open += shares * open_price
                total_high += shares * high_price
                total_low += shares * low_price
                total_close += shares * close_price
                total_volume += volume
                # 更新上一交易日价格
                last_prices[stock_code] = close_price

            missing_stocks = set(holdings.keys()) - set(valid_stocks)
            for stock_code in missing_stocks:
                shares = holdings[stock_code]
                last_price = last_prices[stock_code]
                total_open += shares * last_price
                total_high += shares * last_price
                total_low += shares * last_price
                total_close += shares * last_price
                # 停牌股票的成交量为 0

            day_data = pd.DataFrame({
                'date': [day],
                'open': [total_open],
                'high': [total_high],
                'low': [total_low],
                'close': [total_close],
                'volume': [total_volume]
            })
            index_data = pd.concat([index_data, day_data], ignore_index=True)

        # 更新当前资金为本月最后一个交易日的收盘市值
        if not index_data.empty:
            current_capital = index_data.iloc[-1]['close']

    # 将指数数据按日期排序
    index_data = index_data.sort_values('date').reset_index(drop=True)
    # 以初始值 1000 进行归一化
    initial_value = index_data.iloc[0]['close']
    index_data['open'] = index_data['open'] / initial_value * initial_capital
    index_data['high'] = index_data['high'] / initial_value * initial_capital
    index_data['low'] = index_data['low'] / initial_value * initial_capital
    index_data['close'] = index_data['close'] / initial_value * initial_capital

    # 输出结果
    index_data.to_csv(f'{csv_name}.csv', index=False)

    tmp_dir = 'tmp'
    if os.path.exists(tmp_dir):
        shutil.rmtree(tmp_dir)
    os.makedirs(tmp_dir, exist_ok=True)
    shutil.copy(f'{csv_name}.csv', tmp_dir)

    data = pd.read_csv(f'{csv_name}.csv', index_col=0, parse_dates=True)
    data.reset_index(inplace=True)
    data['symbol'] = f'{csv_name}'
    data['date'] = data['date'].dt.strftime('%Y/%m/%d')
    data.to_csv(f'{tmp_dir}/{csv_name}.csv', index=False)

    DumpDataUpdate(csv_path=tmp_dir, qlib_dir=os.path.expanduser('~/qlib_bin_today'),
                include_fields='open,close,high,low,volume,amount,change,vwap,adjclose,factor').dump()


if __name__ == '__main__':
    fire.Fire(main)
