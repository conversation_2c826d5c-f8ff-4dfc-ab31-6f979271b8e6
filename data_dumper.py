import datetime
import os
import re

import fire
import pandas as pd
from pandas import MultiIndex
from qlib.data import D

from configs import config_info
from util import load_model, load_stock_data, get_feature_names, prepare_data_for_model, predict_proba, \
    prepare_bt_pred_data, init_qlib


class DataDumper:
    def __init__(self, prefix='y10_302_sel_b', interval=95, save_path='pred.csv', start='20120101', end='20240611', feature=None,
                 count=458):
        self.prefix = prefix
        self.interval = interval
        self.save_path = save_path
        self.start = start
        self.end = end
        self.feature = feature
        self.count = count

    @staticmethod
    def _modify_index(df):
        instruments = D.list_instruments(D.instruments(market='all'))
        sec_id_map = {code[-6:]: code for code in instruments}
        if not isinstance(df.index, MultiIndex):
            df.set_index(['date', 'sec_id'], inplace=True)

        drop_names = ['vwap5', 'rank', 'close','volume','high','low','open','adjclose','amount','return','factor','vwap','benchmark_open','benchmark_close','return_1D','flag_1D','return_5D','flag_5D','return_10D','flag_10D']
        df.drop(columns =[col for col in drop_names if col in df.columns], inplace=True)
        index0 = df.index.get_level_values(0)
        index1 = df.index.get_level_values(1)
        new_index1 = [sec_id_map[sec_id] if sec_id in sec_id_map else sec_id for sec_id in index1]
        new_index = pd.MultiIndex.from_arrays([index0, new_index1], names=['datetime', 'instrument'])
        df.index = new_index
        df.sort_index(inplace=True)

    @staticmethod
    def _dump_predict_by_date(start, end, model_path):
        model = load_model(model_path)
        alpha_type = config_info['alpha_type']
        df = load_stock_data(alpha_type, config_info['market'])
        factor_names = get_feature_names(alpha_type, config_info['market'], config_info['period_n'])
        test_data = prepare_data_for_model(df, factor_names, config_info['period_n'], start,
                                           end)
        predicted_data = predict_proba(model, test_data)
        pred = prepare_bt_pred_data(predicted_data, df, 0)
        return pred

    def dump_feature(self):
        alpha_type = config_info['alpha_type']
        config_info['auto_feature_select'] = True
        df = load_stock_data(alpha_type, config_info['market'])
        factor_names = get_feature_names(alpha_type, config_info['market'], config_info['period_n'], self.count)
        factor_names = [name for name in factor_names if re.match(self.feature, name)]

        ret = prepare_data_for_model(df, factor_names, config_info['period_n'], self.start,
                                           self.end)
        ret.drop(columns=['return'], inplace=True)
        DataDumper._modify_index(ret)
        self._save(ret)

    def dump_predict(self):
        directory = 'models'

        file_names = [f for f in os.listdir(directory) if f.startswith(f'{self.prefix}_')]
        date_pattern = re.compile(rf"_i{self.interval}_(\d{{8}})")
        dates = []
        dates_to_files = {}

        for file_name in file_names:
            match = date_pattern.search(file_name)
            if match:
                date = datetime.datetime.strptime(match.group(1), '%Y%m%d')
                dates.append(date)
                dates_to_files[date] = file_name
        dates = list(set(dates))
        dates.sort()
        df = []
        for i in range(len(dates) - 1):
            start_date = dates[i] + datetime.timedelta(days=1)
            model_path = dates_to_files[dates[i]]
            end_date = dates[i + 1]
            res = DataDumper._dump_predict_by_date(start_date, end_date, model_path)
            df.append(res)

        start_date = dates[-1] + datetime.timedelta(days=1)
        model_path = dates_to_files[dates[-1]]
        end_date = datetime.date.today()
        res = DataDumper._dump_predict_by_date(start_date, end_date, model_path)
        df.append(res)

        df = pd.concat(df, axis=0)
        DataDumper._modify_index(df)

        self._save(df)
        return df

    def _save(self, df):
        if self.save_path is not None:
            if self.save_path.endswith(".csv"):
                df.to_csv(self.save_path, index=True)
            else:
                df.to_pickle(self.save_path)


if __name__ == '__main__':
    init_qlib()
    fire.Fire(DataDumper)
