import fire
import pandas as pd
import pywencai
from tqdm import tqdm
from util import *
import time
from autogluon.tabular import TabularPredictor
import os


class WencaiScoreResearch:
    trade_log_path: str = 'trade_397.csv'

    @staticmethod
    def update_scores(date, score_path):
        if score_path is not None:
            try:
                existing_data = pd.read_csv(score_path, parse_dates=True, index_col=[0, 1])
            except FileNotFoundError:
                existing_data = pd.DataFrame()
        else:
            existing_data = pd.DataFrame()
        current_date = pd.to_datetime(date)
        if not existing_data.empty and current_date in existing_data.index.get_level_values(0):
            return

        max_call_per_minute = 10  # 最大每分钟调用次数
        if not hasattr(WencaiScoreResearch.update_scores, "_last_call"):
            WencaiScoreResearch.update_scores._last_call = 0
        elapsed = time.time() - WencaiScoreResearch.update_scores._last_call
        min_interval = 60.0 / max_call_per_minute
        if elapsed < min_interval:
            time.sleep(min_interval - elapsed)

        ret = pywencai.get(loop=True,
                           query=f'在{date}日{config_info["wencai_prompt"]}，在{date}日的技术面、消息面、基本面、行业面、牛叉诊股综合评分，以及{date}日技术面、消息面、基本面、行业面、牛叉诊股综合评分的日环比增长率')
        WencaiScoreResearch.update_scores._last_call = time.time()
        ret.columns = [col.split('[')[0].strip() for col in ret.columns]
        ret.reset_index(drop=True, inplace=True)
        ret['date'] = pd.to_datetime(date)
        ret.set_index(['date', 'code'], inplace=True)
        ret = ret[['技术面评分', '消息面评分', '基本面评分', '行业面评分', '牛叉诊股综合评分', '技术面评分环比增长率',
                   '消息面评分环比增长率', '基本面评分环比增长率', '行业面评分环比增长率', '牛叉诊股综合评分环比增长率',
                   '资金面评分']]
        combined_data = pd.concat([existing_data, ret])
        combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
        if score_path is not None:
            combined_data.to_csv(score_path, index=True)

    @staticmethod
    def prepare_scores_by_trade_data(score_path, from_date, to_date):
        from_date_dt = pd.to_datetime(from_date)
        to_date_dt = pd.to_datetime(to_date)
        grouped_data = D.calendar(start_time=from_date_dt.strftime('%Y-%m-%d'),
                                  end_time=to_date_dt.strftime('%Y-%m-%d'), freq="day", future=False)

        for date in tqdm(grouped_data, total=len(grouped_data), desc="Updating stock scores"):
            formatted_date = pd.to_datetime(date).strftime('%Y%m%d')
            WencaiScoreResearch.update_scores(formatted_date, score_path)
        print(f"Stock scores updated and saved to {score_path}")

    @staticmethod
    def print_score_data_corr(score_path):
        df = load_stock_data(config_info['alpha_type'], config_info['market'])
        df = df[['return_1D', 'return_10D']]
        scores = pd.read_csv(score_path, index_col=[0, 1], parse_dates=True, dtype={1: str})
        scores.reset_index(inplace=True)
        scores['code'] = scores['code'].str.zfill(6)
        scores.rename(columns={'code': 'sec_id'}, inplace=True)
        scores.set_index(['date', 'sec_id'], inplace=True)
        combine = scores.merge(df, left_index=True, right_index=True, how='inner')
        combine['return_1D'] = combine.groupby(level=0)['return_1D'].rank(pct=True, ascending=True)
        combine['return_10D'] = combine.groupby(level=0)['return_10D'].rank(pct=True, ascending=True)
        print(combine.corr()[['return_1D', 'return_10D']])

    @staticmethod
    def train_model(model_path, score_path, from_date, to_date):
        df = load_stock_data(config_info['alpha_type'], config_info['market'])
        df = df[['return_10D']]

        scores = pd.read_csv(score_path, index_col=[0, 1], parse_dates=True, dtype={1: str})
        scores.reset_index(inplace=True)
        scores['code'] = scores['code'].str.zfill(6)
        scores.rename(columns={'code': 'sec_id'}, inplace=True)
        scores.set_index(['date', 'sec_id'], inplace=True)
        combine = scores.merge(df, left_index=True, right_index=True, how='inner')
        combine['return_10D'] = combine.groupby(level=0)['return_10D'].rank(pct=True, ascending=True)

        combine = combine.reset_index()
        combine['date'] = pd.to_datetime(combine['date'])
        from_date = pd.to_datetime(from_date)
        to_date = pd.to_datetime(to_date)
        train_data = combine[(combine['date'] <= to_date) & (combine['date'] >= from_date)]

        train_data = train_data.drop(columns=['date', 'sec_id'])

        # hyperparameters = {'GBM':
        #     [{
        #         'device_type': 'gpu',
        #         'seed': 0,
        #         'bagging_seed': 0,
        #         'feature_fraction_seed': 0,
        #         'data_random_seed': 0,
        #         'feature_fraction': 0.9,  # 禁用特征子采样
        #         'gpu_use_dp': True,
        #         "learning_rate": 0.03,
        #         "num_leaves": 128,
        #         "min_data_in_leaf": 5,
        #         'ag_args': dict(model_type="GBM", name_suffix="Large", hyperparameter_tune_kwargs=None, priority=0),
        #     }, {"extra_trees": True, "ag_args": {"name_suffix": "XT"}},
        #         {}, ],
        #     'XGB': {
        #         'seed': 0,
        #         'tree_method': 'gpu_hist',
        #         'predictor': 'gpu_predictor',
        #     }
        # }
        hyperparameters={'LR': {}}

        predictor = TabularPredictor(label='return_10D', path=f'models/{model_path}').fit(train_data, keep_only_best=True,
                                                                              save_space=True,
                                                                              hyperparameters=hyperparameters,
                                                                              presets='medium_quality')
        predictor.save()
        print(f"Model saved to {model_path}")

    @staticmethod
    def predict(model_path, score_path, predict_path, from_date, to_date, trade_data_only=False):
        df = load_stock_data(config_info['alpha_type'], config_info['market'])
        if trade_data_only and os.path.exists(WencaiScoreResearch.trade_log_path):
            trade_data = pd.read_csv(WencaiScoreResearch.trade_log_path, dtype={'sec_id': str}, parse_dates=['date'])
            trade_data['sec_id'] = trade_data['sec_id'].astype(str).str.zfill(6)
            trade_data.set_index(['date', 'sec_id'], inplace=True)
            df = pd.merge(df, trade_data, left_index=True, right_index=True, how='inner')
        df = df[['return_10D']]

        scores = pd.read_csv(score_path, index_col=[0, 1], parse_dates=True, dtype={1: str})
        scores.reset_index(inplace=True)
        scores['code'] = scores['code'].str.zfill(6)
        scores.rename(columns={'code': 'sec_id'}, inplace=True)
        scores = scores[(scores['date'] <= to_date) & (scores['date'] >= from_date)]
        scores.set_index(['date', 'sec_id'], inplace=True)
        test_data = scores.merge(df, left_index=True, right_index=True, how='inner')
        test_data['return_10D'] = test_data.groupby(level=0)['return_10D'].rank(pct=True, ascending=True)

        predictor = TabularPredictor.load(f'models/{model_path}')
        predictions = predictor.predict(test_data)
        predictions = pd.DataFrame(predictions, index=predictions.index, columns=['return_10D'])
        predictions.rename(columns={'return_10D': 'predict'}, inplace=True)

        if os.path.exists(predict_path):
            old_preds = pd.read_csv(predict_path, index_col=[0, 1], parse_dates=True, dtype={1: str})
            old_preds.index = pd.MultiIndex.from_tuples(
                [(d, str(code).zfill(6)) for d, code in old_preds.index],
                names=old_preds.index.names
            )
            old_preds.update(predictions)
            new_entries = predictions.loc[~predictions.index.isin(old_preds.index)]
            updated_preds = pd.concat([old_preds, new_entries]).sort_index()
        else:
            updated_preds = predictions.copy()

        updated_preds.to_csv(predict_path)

        test_data = pd.merge(test_data, predictions, left_index=True, right_index=True, how='inner')
        corr_value = test_data.corr().loc['predict', 'return_10D']
        print("Correlation between predicted value and actual return10D:", corr_value)


if __name__ == '__main__':
    init_logger()
    init_qlib()
    fire.Fire(WencaiScoreResearch)
    # WencaiScoreResearch.prepare_scores_by_trade_data( 'save_res.csv', '20230101', '20231231')
    # train_return10d_model('wencai_model', 'save_res.csv', '20240101', '20241231')
    # WencaiScoreResearch.train_model('wencai_model', 'save_res.csv', '20240101', '20241215')
    # WencaiScoreResearch.predict('wencai_model', 'save_res.csv', 'wencai-predict.csv',
    #                             '20250101', '20250312', trade_data_only=False)
