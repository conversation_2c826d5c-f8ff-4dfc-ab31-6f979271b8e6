<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="78" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="31">
            <item index="0" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="1" class="java.lang.String" itemvalue="tqdm" />
            <item index="2" class="java.lang.String" itemvalue="openai" />
            <item index="3" class="java.lang.String" itemvalue="stable_baselines3" />
            <item index="4" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="5" class="java.lang.String" itemvalue="akshare" />
            <item index="6" class="java.lang.String" itemvalue="Requests" />
            <item index="7" class="java.lang.String" itemvalue="pyqlib" />
            <item index="8" class="java.lang.String" itemvalue="scipy" />
            <item index="9" class="java.lang.String" itemvalue="pathos" />
            <item index="10" class="java.lang.String" itemvalue="transformers" />
            <item index="11" class="java.lang.String" itemvalue="TA_Lib" />
            <item index="12" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="13" class="java.lang.String" itemvalue="backtrader_plotting" />
            <item index="14" class="java.lang.String" itemvalue="torch" />
            <item index="15" class="java.lang.String" itemvalue="finrl" />
            <item index="16" class="java.lang.String" itemvalue="numpy" />
            <item index="17" class="java.lang.String" itemvalue="requests" />
            <item index="18" class="java.lang.String" itemvalue="Boruta" />
            <item index="19" class="java.lang.String" itemvalue="azure-cognitiveservices-speech" />
            <item index="20" class="java.lang.String" itemvalue="pandas" />
            <item index="21" class="java.lang.String" itemvalue="autogluon" />
            <item index="22" class="java.lang.String" itemvalue="slack_sdk" />
            <item index="23" class="java.lang.String" itemvalue="jupyter" />
            <item index="24" class="java.lang.String" itemvalue="alphalens" />
            <item index="25" class="java.lang.String" itemvalue="backtrader" />
            <item index="26" class="java.lang.String" itemvalue="langchain" />
            <item index="27" class="java.lang.String" itemvalue="matplotlib" />
            <item index="28" class="java.lang.String" itemvalue="lightgbm" />
            <item index="29" class="java.lang.String" itemvalue="mlflow" />
            <item index="30" class="java.lang.String" itemvalue="hyperopt" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
          <option value="N803" />
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="super.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>