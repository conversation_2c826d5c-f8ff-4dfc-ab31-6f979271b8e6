import os.path

from alphalens.tears import create_full_tear_sheet
from sklearn.linear_model import LinearRegression

import alpha_101
import alpha_120
import alpha_191
import util
from alphabase import Alphas
from util import *


def calculate_beta_and_residuals(stock_data, bm_data, window=20):
    stock_data['close_prev'] = stock_data.groupby('sec_id')['close'].shift(1)
    stock_data['stock_returns'] = stock_data['close'] / stock_data['close_prev'] - 1
    stock_data.drop(columns=['close_prev'], inplace=True)  # 清理临时列

    bm_data['bm_returns'] = bm_data['close'].pct_change()

    # 准备一个DataFrame来存储beta值
    betas_list = []

    # 初始化LinearRegression模型
    model = LinearRegression()

    # 对每个股票计算beta
    for sec_id in stock_data['sec_id'].unique():
        stock_df = stock_data[stock_data['sec_id'] == sec_id].dropna()
        common_dates = stock_df.index.intersection(bm_data.index)
        Y = stock_df.loc[common_dates, 'stock_returns'].values.reshape(-1, 1)
        X = bm_data.loc[common_dates, 'bm_returns'].values.reshape(-1, 1)

        # 检查数据点数量，确保足够计算20天滚动窗口的beta
        if len(X) >= 20:
            for i in range(0, len(X) - 19):
                model.fit(X[i:i + 20], Y[i:i + 20])
                beta = model.coef_[0][0]
                date = common_dates[i + 19]
                # 将临时DataFrame添加到列表中
                betas_list.append(pd.DataFrame({'date': [date], 'sec_id': [sec_id], 'beta': [beta]}))
    betas = pd.concat(betas_list, ignore_index=True)
    # 调整价格
    adjusted_prices = pd.DataFrame()
    for sec_id in stock_data['sec_id'].unique():
        stock_df = stock_data[stock_data['sec_id'] == sec_id].copy()
        stock_df['close_adj'] = stock_df['close']
        stock_betas = betas[betas['sec_id'] == sec_id]
        for date, beta in zip(stock_betas['date'], stock_betas['beta']):
            if date in stock_df.index:
                # 获取前一天调整后的收盘价
                prev_close = stock_df.loc[:date].iloc[-2]['close'] if date > stock_df.index[0] else \
                    stock_df.loc[date]['close']

                prev_close_adj = stock_df.loc[date]['close'] if date <= stock_df.index[0] else \
                    stock_df.loc[:date].iloc[-2]['close_adj']
                for col in ['open', 'high', 'low', 'close']:
                    # 计算价格相对于前一天调整后收盘价的变化率
                    change = stock_df.loc[date][col] / prev_close - 1
                    # 使用beta调整变化率
                    adj_change = change - beta * bm_data.loc[date]['bm_returns']

                    # 计算调整后的价格
                    stock_df.loc[date, f'{col}_adj'] = prev_close_adj * (1 + adj_change)
                    stock_df.loc[date, 'beta'] = beta
        adjusted_prices = pd.concat([adjusted_prices, stock_df])

    # 清理和重命名列
    adjusted_prices.drop(columns=['open', 'high', 'low', 'close', 'stock_returns'], inplace=True)
    adjusted_prices.rename(columns={'open_adj': 'open', 'high_adj': 'high', 'low_adj': 'low', 'close_adj': 'close'},
                           inplace=True)

    # 输出结果
    return adjusted_prices


def research_adjusted_alpha(start, end, save_path='alpha', with_adjust=False, selected_alpha_names=None):
    start_prev_month = pd.to_datetime(start) - pd.Timedelta(days=30)
    bm_data = pd.read_csv(f'{config_info["market"]}.csv', index_col=0)
    bm_data.index = pd.to_datetime(bm_data.index)
    config_info['generate_ins_date'] = (start, end)
    df_all, _ = Alphas.get_stocks_data_by_date(end, start_prev_month)
    if with_adjust:
        df_all = calculate_beta_and_residuals(df_all, bm_data, 20)
    df_all = df_all[df_all.index.get_level_values(0) >= pd.to_datetime(start)]

    stock_data = df_all.pivot(index='date', columns='sec_id')
    if not os.path.exists(save_path):
        os.mkdir(save_path)

    alpha_classes = [alpha_101.Alphas101, alpha_191.Alphas191]
    if config_info['exp_features']:
        alpha_classes.append(alpha_120.Alphas120)
    for alpha_class in alpha_classes:
        alpha = alpha_class(stock_data)
        alpha_names = alpha_class.get_alpha_methods(alpha)
        if selected_alpha_names is not None and len(selected_alpha_names) >= 0:
            alpha_names = [name for name in selected_alpha_names if name in alpha_names]

        caculate_ic_factors(alpha_class, alpha, alpha_names, stock_data, save_path)


def check_increasing_trend(group):
    # 检查列表中的值是否按升序排列
    return all(x < y for x, y in zip(group, group[1:]))


def trend_over_days(mean_1d_by_date_and_quantile, days=4):
    result = pd.Series(dtype=bool)
    for date in mean_1d_by_date_and_quantile.index.unique():
        # 获取给定日期前3天的数据
        start_date = date - pd.Timedelta(days=days)
        subset = mean_1d_by_date_and_quantile.loc[start_date:date]

        # 计算每个factor_quantile在这些天的平均值
        avg_by_quantile = subset.groupby('factor_quantile')['1D'].mean()
        avg_by_quantile = avg_by_quantile.iloc[[0, 2, 4]]
        # 检查平均值是否随factor_quantile的增加而增加
        result[date] = check_increasing_trend(avg_by_quantile.values)

    return result.shift(1)


def assign_group_by_ic2(factors, ic):
    factors_returns = factors.groupby(['date', 'factor_quantile'])['1D'].mean().reset_index()
    factors_returns['date'] = pd.to_datetime(factors_returns['date'])
    factors_returns.set_index('date', inplace=True)

    # groups = trend_over_days(factors_returns, 4)
    groups = trend_over_days(factors_returns, 9)
    groups = groups | groups.shift(1)
    for date, trend in groups.items():
        factors.loc[(date,), 'group'] = trend
    return factors


def assign_group_by_ic(factors, ic):
    # 计算IC的滚动均值
    ic_rolling_4d = ic.rolling(window=6).mean().shift(1)
    ic_rolling_2d = ic.rolling(window=2).mean().shift(1)
    ic_rolling_prev_2d = ic.shift(2).rolling(window=2).mean().shift(1)

    # 为factors创建一个空的"group"列
    factors['group'] = 'g0'

    # 遍历factors的索引（日期和资产）
    for date, asset in factors.index:
        # 检查日期是否在ic的索引中
        if date in ic.index:
            # 根据条件分配组别
            if (ic_rolling_2d.loc[date, '1D'] > 0.02) and (ic_rolling_2d.loc[date, '1D'] > ic_rolling_prev_2d.loc[
                date, '1D']):
                group = 'g1'
            else:
                group = 'g2'
            # 更新factors中的"group"列
            factors.loc[(date, asset), 'group'] = group

    return factors


def caculate_ic_factors(alpha_cls, alpha, alpha_names, stock_data, save_path):
    for alpha_name in alpha_names:
        print(f'-----------{alpha_name}-------------')
        factor = getattr(alpha_cls, alpha_name)
        ret = factor(alpha)
        ret = ret.reset_index().melt(id_vars='date', var_name='sec_id', value_name=alpha_name)
        ret.set_index(['date', 'sec_id'], inplace=True)
        try:
            factors = alphalens.utils.get_clean_factor_and_forward_returns(ret, stock_data['close'],
                                                                           bins=None,
                                                                           periods=(1, 5, 10), quantiles=5,
                                                                           max_loss=0.35)
            ic = perf.mean_information_coefficient(factors)
            print(ic)
            ic = perf.factor_information_coefficient(factors)

            factors = assign_group_by_ic2(factors, ic)
            print(factors['group'].value_counts())
            # 对每一个factor_quantile和group, 输出列1D的均值
            mean_returns = factors.groupby('factor_quantile').apply(
                lambda x: x.groupby('group').apply(lambda y: y.mean()))
            mean_returns.to_csv(f'{save_path}/{alpha_name}_mean_returns.csv')
            print(mean_returns)
            factors.drop(columns=['group'], inplace=True)

            create_full_tear_sheet(factors, long_short=False, by_group=False)
            pd.to_pickle(ret, f'{save_path}/{alpha_name}.pkl')
            print(f'{alpha_name} saved.')
        except MaxLossExceededError as e:
            print('skip alpha ', alpha_name)


def dump_alphas(start: str, end: str, **kwargs):
    start = start.replace('-', '')
    end = end.replace('-', '')
    util.init_qlib()
    util.init_logger()
    research_adjusted_alpha(start, end, **kwargs)


if __name__ == '__main__':
    # fire.Fire(dump_alphas)
    util.init_qlib()
    start = '20220101'
    end = '20221231'
    research_adjusted_alpha(start, end, selected_alpha_names=['alpha006'])
