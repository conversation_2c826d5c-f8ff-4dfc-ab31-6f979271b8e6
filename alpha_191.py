from numpy import log
from scipy.stats import percentileofscore

from alphabase import *


def Log(sr):
    return np.log(sr)


def Rank(sr):
    return sr.rank(axis=1, method='min', pct=True)


def Delta(sr, period):
    return sr.diff(period)


def DeltaRate(sr, period):
    return sr.diff(period) / sr.shift(period)


def Delay(sr, period):
    return sr.shift(period)


def Corr(x, y, window):
    return x.fillna(0).rolling(window).corr(y.fillna(0)).fillna(0).replace([np.inf, -np.inf], 0)


def Cov(x, y, window):
    return x.fillna(0).rolling(window).cov(y.fillna(0))


def Sum(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).sum()


def Prod(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).apply(np.prod)


def Mean(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).mean()


def Std(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).std()


def RollingRank(na):
    return percentileofscore(na, na[-1], 'rank') / 100


def Tsrank(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).apply(RollingRank)


def Tsmax(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).max()


def Tsmin(sr, window):
    return sr.rolling(window, min_periods=min_periods_window(window)).min()


def Sign(sr):
    return np.sign(sr)


def Max(sr1, sr2):
    return np.maximum(sr1, sr2)


def Min(sr1, sr2):
    return np.minimum(sr1, sr2)


def Rowmax(sr):
    return sr.max(axis=1)


def Rowmin(sr):
    return sr.min(axis=1)


def Sma(sr, n, m):
    return sr.ewm(alpha=m / n, adjust=False).mean()


def Abs(sr):
    return sr.abs()


def Sequence(n):
    return np.arange(1, n + 1)


def Regbeta(sr, x):
    window = len(x)
    return sr.rolling(window).apply(lambda y: np.polyfit(x, y, deg=1)[0])


def Decaylinear(df, period):
    weights = np.array(range(1, period + 1))
    sum_weights = np.sum(weights)
    return df.rolling(period).apply(lambda x: np.sum(weights * x.fillna(0)) / sum_weights)


def Lowday(sr, window):
    return sr.rolling(window).apply(lambda x: len(x) - x.values.argmin())


def Highday(sr, window):
    return sr.rolling(window).apply(lambda x: len(x) - x.values.argmax())


def Wma(sr, window):
    weights = np.array(range(window - 1, -1, -1))
    weights = np.power(0.9, weights)
    sum_weights = np.sum(weights)

    return sr.rolling(window).apply(lambda x: np.sum(weights * x.fillna(0)) / sum_weights)


def Count(cond, window):
    return cond.rolling(window).apply(lambda x: x.sum())


def Sumif(sr, window, cond):
    sr[~cond] = 0
    return sr.rolling(window).sum()


def Returns(df):
    return df.rolling(2).apply(lambda x: x.iloc[-1] / x.iloc[0]) - 1


class Alphas191(Alphas):
    def __init__(self, df_data):
        self.open = df_data['open']
        self.high = df_data['high']
        self.low = df_data['low']
        self.close = df_data['close']
        self.volume = df_data['volume']
        self.returns = df_data['change']
        self.vwap = df_data['vwap']
        self.close_prev = df_data['close'].shift(1)
        self.amount = df_data['amount']

        self.benchmark_open = df_data['benchmark_open']
        self.benchmark_close = df_data['benchmark_close']

    def gtja001(self):
        return -1 * Corr(Rank(DeltaRate(log(self.volume), 1)), Rank(((self.close - self.open) / self.open)), 6)

    def gtja002(self):
        return -1 * Delta((((self.close - self.low) - (self.high - self.close)) / (self.high - self.low)), 1)

    def gtja003(self):
        cond1 = (self.close == Delay(self.close, 1))
        cond2 = (self.close > Delay(self.close, 1))
        cond3 = (self.close < Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond1] = 0
        part[cond2] = (self.close - Min(self.low, Delay(self.close, 1))) / self.close
        part[cond3] = (self.close - Max(self.high, Delay(self.close, 1))) / self.close
        return Sum(part, 6)

    def gtja004(self):
        cond1 = ((Sum(self.close, 8) / 8 + Std(self.close, 8)) < Sum(self.close, 2) / 2)
        cond2 = ((Sum(self.close, 8) / 8 + Std(self.close, 8)) > Sum(self.close, 2) / 2)
        cond3 = ((Sum(self.close, 8) / 8 + Std(self.close, 8)) == Sum(self.close, 2) / 2)
        cond4 = (self.volume / Mean(self.volume, 20) >= 1)
        part = self.close.copy(deep=True)
        part[cond1] = -1
        part[cond2] = 1
        part[cond3] = -1
        part[cond3 & cond4] = 1

        return part

    def gtja006(self):
        return -1 * Rank(Sign(Delta(((self.open * 0.85) + (self.high * 0.15)), 4)))

    def gtja008(self):
        return Rank(DeltaRate(((((self.high + self.low) / 2) * 0.2) + (self.vwap * 0.8)), 4) * -1)

    def gtja009(self):
        return Sma(((self.high + self.low) / 2 - (Delay(self.high, 1) + Delay(self.low, 1)) / 2) * (
                self.high - self.low) / DeltaRate(self.volume, 1), 7, 2)

    def gtja010(self):
        cond = (self.returns < 0)
        part = self.returns.copy(deep=True)
        part[cond] = Std(self.returns, 20)
        part = part ** 2
        return Rank(Tsmax(part, 5))

    def gtja011(self):
        return Sum(
            ((self.close - self.low) - (self.high - self.close)) / (self.high - self.low) * DeltaRate(self.volume, 6),
            6)

    def gtja012(self):
        return (Rank((self.open - (Sum(self.vwap, 10) / 10)))) / (-1 * (Rank(Abs((self.close - self.vwap)))))

    def gtja015(self):
        return self.open / Delay(self.close, 1) - 1

    #重复
    def gtja016(self):
        return Tsmax(Rank(Corr(Rank(self.volume), Rank(self.vwap), 5)), 5)

    def gtja017(self):
        return Rank((self.vwap / Tsmax(self.vwap, 15))) ** DeltaRate(self.close, 5)

    def gtja018(self):
        return self.close / Delay(self.close, 5)

    def gtja019(self):
        cond1 = (self.close < Delay(self.close, 5))
        cond2 = (self.close == Delay(self.close, 5))
        cond3 = (self.close > Delay(self.close, 5))
        part = self.close.copy(deep=True)
        part[cond1] = (self.close - Delay(self.close, 5)) / Delay(self.close, 5)
        part[cond2] = 0
        part[cond3] = (self.close - Delay(self.close, 5)) / self.close

        return part

    def gtja020(self):
        return (self.close - Delay(self.close, 6)) / Delay(self.close, 6) * 100

    def gtja021(self):
        return Regbeta(Mean(self.close, 6), Sequence(6))

    def gtja022(self):
        return Sma(((self.close - Mean(self.close, 6)) / Mean(self.close, 6) - Delay(
            (self.close - Mean(self.close, 6)) / Mean(self.close, 6), 3)), 12, 1)

    def gtja023(self):
        cond = (self.close > Delay(self.close, 1))
        part1 = self.close.copy(deep=True)
        part1[cond] = Std(self.close, 20)
        part1[~cond] = 0
        part2 = self.close.copy(deep=True)
        part2[~cond] = Std(self.close, 20)
        part2[cond] = 0

        return 100 * Sma(part1, 20, 1) / (Sma(part1, 20, 1) + Sma(part2, 20, 1))

    def gtja024(self):
        return Sma(self.close - Delay(self.close, 5), 5, 1)

    def gtja025(self):
        return ((-1 * Rank(
            (DeltaRate(self.close, 7) * (1 - Rank(Decaylinear((self.volume / Mean(self.volume, 20)), 9)))))) * (
                        1 + Rank(Sum(self.returns, 250))))

    def gtja026(self):
        return ((Sum(self.close, 7) / 7) / self.close) + (Corr(self.vwap, Delay(self.close, 5), 230))

    def gtja027(self):
        A = (self.close - Delay(self.close, 3)) / Delay(self.close, 3) * 100 + (
                self.close - Delay(self.close, 6)) / Delay(self.close, 6) * 100
        return Wma(A, 12)

    def gtja028(self):
        return 3 * Sma((self.close - Tsmin(self.low, 9)) / (Tsmax(self.high, 9) - Tsmin(self.low, 9)) * 100, 3,
                       1) - 2 * Sma(
            Sma((self.close - Tsmin(self.low, 9)) / (Tsmax(self.high, 9) - Tsmax(self.low, 9)) * 100, 3, 1), 3, 1)

    def gtja029(self):
        return (self.close - Delay(self.close, 6)) / Delay(self.close, 6) * self.volume

    def gtja034(self):
        return Mean(self.close, 12) / self.close

    def gtja035(self):
        return (Min(Rank(Decaylinear(Delta(self.open, 1), 15)),
                    Rank(Decaylinear(Corr(self.volume, self.open, 17), 7))) * -1)

    def gtja036(self):
        return Rank(Sum(Corr(Rank(self.volume), Rank(self.vwap), 6), 2))

    def gtja038(self):
        cond = ((Sum(self.high, 20) / 20) < self.high)
        part = self.close.copy(deep=True)
        part[cond] = -1 * Delta(self.high, 2)
        part[~cond] = 0

        return part

    def gtja039(self):
        return ((Rank(Decaylinear(DeltaRate(self.close, 2), 8)) - Rank(
            Decaylinear(Corr(((self.vwap * 0.3) + (self.open * 0.7)), Sum(Mean(self.volume, 180), 37), 14), 12))) * -1)

    def gtja040(self):
        cond = (self.close > Delay(self.close, 1))
        part1 = self.close.copy(deep=True)
        part1[cond] = self.volume
        part1[~cond] = 0
        part2 = self.close.copy(deep=True)
        part2[~cond] = self.volume
        part2[cond] = 0

        return Sum(part1, 26) / Sum(part2, 26) * 100

    def gtja041(self):
        return Rank(Tsmax(Delta(self.vwap, 3), 5)) * -1

    def gtja043(self):
        cond1 = (self.close > Delay(self.close, 1))
        cond2 = (self.close < Delay(self.close, 1))
        cond3 = (self.close == Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond1] = self.volume
        part[cond2] = -self.volume
        part[cond3] = 0

        return Sum(part, 6) / self.volume

    def gtja044(self):
        return (Tsrank(Decaylinear(Corr(self.low, Mean(self.volume, 10), 7), 6), 4) + Tsrank(
            Decaylinear(Delta(self.vwap, 3), 10), 15))

    def gtja045(self):
        return (Rank(DeltaRate((self.close * 0.6 + self.open * 0.4), 1)) * Rank(
            Corr(self.vwap, Mean(self.volume, 150), 15)))

    def gtja047(self):
        return Sma((Tsmax(self.high, 6) - self.close) / (Tsmax(self.high, 6) - Tsmin(self.low, 6)) * 100, 9, 1)

    def gtja048(self):
        return (-1 * ((Rank(((Sign((self.close - Delay(self.close, 1))) + Sign(
            (Delay(self.close, 1) - Delay(self.close, 2)))) + Sign(
            (Delay(self.close, 2) - Delay(self.close, 3)))))) * Sum(self.volume, 5)) / Sum(self.volume, 20))

    def gtja050(self):
        cond = ((self.high + self.low) <= (Delay(self.high, 1) + Delay(self.low, 1)))
        part1 = self.close.copy(deep=True)
        part1[cond] = 0
        part1[~cond] = Max(Abs(self.high - Delay(self.high, 1)), Abs(self.low - Delay(self.low, 1)))
        part2 = self.close.copy(deep=True)
        part2[~cond] = 0
        part2[cond] = Max(Abs(self.high - Delay(self.high, 1)), Abs(self.low - Delay(self.low, 1)))

        return (Sum(part1, 12) - Sum(part2, 12)) / (Sum(part1, 12) + Sum(part2, 12))

    def gtja052(self):
        return Sum(Max(self.high - Delay((self.high + self.low + self.close) / 3, 1), 0), 26) / Sum(
            Max(Delay((self.high + self.low + self.close) / 3, 1) - self.low, 0), 26) * 100

    def gtja053(self):
        cond = (self.close > Delay(self.close, 1))
        return Count(cond, 12) / 12 * 100

    def gtja054(self):
        return (-1 * Rank(
            (Std(Abs(self.close - self.open), 250) / (self.close - self.open)) + Corr(self.close, self.open, 10)))

    def gtja055(self):
        A = Abs(self.high - Delay(self.close, 1))
        B = Abs(self.low - Delay(self.close, 1))
        C = Abs(self.high - Delay(self.low, 1))
        cond1 = ((A > B) & (A > C))
        cond2 = ((B > C) & (B > A))
        cond3 = ((C >= A) & (C >= B))
        part0 = 16 * (self.close + (self.close - self.open) / 2 - Delay(self.open, 1))
        part1 = self.close.copy(deep=True)
        part1.loc[:, :] = 0
        part1[cond1] = Abs(self.high - Delay(self.close, 1)) + Abs(self.low - Delay(self.close, 1)) / 2 + Abs(
            Delay(self.close, 1) - Delay(self.open, 1)) / 4
        part1[cond2] = Abs(self.low - Delay(self.close, 1)) + Abs(self.high - Delay(self.close, 1)) / 2 + Abs(
            Delay(self.close, 1) - Delay(self.open, 1)) / 4
        part1[cond3] = Abs(self.high - Delay(self.low, 1)) + Abs(Delay(self.close, 1) - Delay(self.open, 1)) / 4
        part2 = Max(Abs(self.high - Delay(self.close, 1)), Abs(self.low - Delay(self.close, 1)))

        return Sum(part0 ** 2 / part1 * part2, 20)

    def gtja056(self):
        A = Rank((self.open - Tsmin(self.open, 12)))
        B = Rank((Rank(Corr(Sum(((self.high + self.low) / 2), 19), Sum(Mean(self.volume, 40), 19), 13)) ** 5))
        cond = (A < B)
        part = self.close.copy(deep=True)
        part[cond] = 1
        part[~cond] = 0
        return part

    def gtja057(self):
        return Sma((self.close - Tsmin(self.low, 9)) / (Tsmax(self.high, 9) - Tsmin(self.low, 9)) * 100, 3, 1)

    def gtja058(self):
        cond = (self.close > Delay(self.close, 1))

        return Count(cond, 20) / 20 * 100

    def gtja059(self):
        cond1 = (self.close == Delay(self.close, 1))
        cond2 = (self.close > Delay(self.close, 1))
        cond3 = (self.close < Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond1] = 0
        part[cond2] = self.close / Min(self.low, Delay(self.close, 1))
        part[cond3] = self.close / Max(self.low, Delay(self.close, 1))

        return Sum(part, 20)

    def gtja060(self):
        return Sum(
            ((self.close - self.low) - (self.high - self.close)) / (self.high - self.low) * DeltaRate(self.volume, 5),
            20)

    def gtja061(self):
        return (Max(Rank(Decaylinear(Delta(self.vwap, 1), 12)),
                    Rank(Decaylinear(Rank(Corr(self.low, Mean(self.volume, 80), 8)), 17))) * -1)

    def gtja063(self):
        return Sma(Max(self.close - Delay(self.close, 1), 0), 6, 1) / Sma(Abs(self.close - Delay(self.close, 1)), 6,
                                                                          1) * 100

    def gtja064(self):
        return (Max(Rank(Decaylinear(Corr(Rank(self.vwap), Rank(self.volume), 4), 4)),
                    Rank(Decaylinear(Tsmax(Corr(Rank(self.close), Rank(Mean(self.volume, 60)), 4), 13), 14))) * -1)

    def gtja066(self):
        return (self.close - Mean(self.close, 6)) / Mean(self.close, 6) * 100

    def gtja067(self):
        a1 = Sma(Max(self.close - Delay(self.close, 1), 0), 24, 1)
        a2 = Sma(Abs(self.close - Delay(self.close, 1)), 24, 1)
        return a1 / a2 * 100

    def gtja068(self):
        return Sma(((self.high + self.low) / 2 - (Delay(self.high, 1) + Delay(self.low, 1)) / 2) * (
                self.high - self.low) / self.volume, 15, 2)

    def gtja069(self):
        cond1 = (self.open <= Delay(self.open, 1))
        cond2 = ~cond1

        DTM = self.close.copy(deep=True)
        DTM[cond1] = 0
        DTM[~cond1] = Max((self.high - self.open), (self.open - Delay(self.open, 1)))

        DBM = self.close.copy(deep=True)
        DBM[cond2] = 0
        DBM[~cond2] = Max((self.open - self.low), (self.open - Delay(self.open, 1)))

        cond3 = (Sum(DTM, 20) > Sum(DBM, 20))
        cond4 = (Sum(DTM, 20) == Sum(DBM, 20))
        cond5 = (Sum(DTM, 20) < Sum(DBM, 20))
        part = self.close.copy(deep=True)
        part[cond3] = (Sum(DTM, 20) - Sum(DBM, 20)) / Sum(DTM, 20)
        part[cond4] = 0
        part[cond5] = (Sum(DTM, 20) - Sum(DBM, 20)) / Sum(DBM, 20)
        return part

    def gtja070(self):
        return Std(self.amount, 6)

    def gtja071(self):
        return (self.close - Mean(self.close, 24)) / Mean(self.close, 24) * 100

    def gtja072(self):
        return Sma((Tsmax(self.high, 6) - self.close) / (Tsmax(self.high, 6) - Tsmin(self.low, 6)) * 100, 15, 1)

    def gtja073(self):
        return ((Tsrank(Decaylinear(Decaylinear(Corr(self.close, self.volume, 10), 16), 4), 5) - Rank(
            Decaylinear(Corr(self.vwap, Mean(self.volume, 30), 4), 3))) * -1)

    def gtja074(self):
        return (Rank(Corr(Sum(((self.low * 0.35) + (self.vwap * 0.65)), 20), Sum(Mean(self.volume, 40), 20), 7)) + Rank(
            Corr(Rank(self.vwap), Rank(self.volume), 6)))

    def gtja075(self):
        return Count(((self.close > self.open) & (self.benchmark_close < self.benchmark_open)), 50) / Count(
            (self.benchmark_close < self.benchmark_open), 50)

    def gtja076(self):
        return Std(Abs((self.close / Delay(self.close, 1) - 1)) / self.volume, 20) / Mean(
            Abs((self.close / Delay(self.close, 1) - 1)) / self.volume, 20)

    def gtja078(self):
        return ((self.high + self.low + self.close) / 3 - Mean((self.high + self.low + self.close) / 3, 12)) / (
                0.015 * Mean(Abs(self.close - Mean((self.high + self.low + self.close) / 3, 12)), 12))

    def gtja079(self):
        return Sma(Max(self.close - Delay(self.close, 1), 0), 12, 1) / Sma(Abs(self.close - Delay(self.close, 1)), 12,
                                                                           1) * 100

    def gtja080(self):
        return (self.volume - Delay(self.volume, 5)) / Delay(self.volume, 5) * 100

    def gtja081(self):
        return Sma(self.volume, 21, 2)

    def gtja082(self):
        return Sma((Tsmax(self.high, 6) - self.close) / (Tsmax(self.high, 6) - Tsmin(self.low, 6)) * 100, 20, 1)

    def gtja084(self):
        cond1 = (self.close > Delay(self.close, 1))
        cond2 = (self.close < Delay(self.close, 1))
        cond3 = (self.close == Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond1] = self.volume
        part[cond2] = 0
        part[cond3] = -self.volume
        return Sum(part, 20) / self.volume

    def gtja086(self):
        A = (((Delay(self.close, 20) - Delay(self.close, 10)) / 10) - ((Delay(self.close, 10) - self.close) / 10))
        cond1 = (A > 0.25)
        cond2 = (A < 0.0)
        cond3 = ((0 <= A) & (A <= 0.25))
        part = self.close.copy(deep=True)
        part[cond1] = -1
        part[cond2] = 1
        part[cond3] = -1 * (self.close - Delay(self.close, 1))
        return part

    def gtja088(self):
        return (self.close - Delay(self.close, 20)) / Delay(self.close, 20) * 100

    def gtja089(self):
        return 2 * (Sma(self.close, 13, 2) - Sma(self.close, 27, 2) - Sma(
            Sma(self.close, 13, 2) - Sma(self.close, 27, 2), 10, 2))

    def gtja090(self):
        return Rank(Corr(Rank(self.vwap), Rank(self.volume), 5)) * -1

    def gtja091(self):
        return (Rank((self.close - Tsmax(self.close, 5))) * Rank(Corr((Mean(self.volume, 40)), self.low, 5))) * -1

    def gtja092(self):
        return (Max(Rank(Decaylinear(Delta(((self.close * 0.35) + (self.vwap * 0.65)), 2), 3)),
                    Tsrank(Decaylinear(Abs(Corr((Mean(self.volume, 180)), self.close, 13)), 5), 15)) * -1)

    def gtja093(self):
        cond = (self.open >= Delay(self.open, 1))
        part = self.close.copy(deep=True)
        part[cond] = 0
        part[~cond] = Max((self.open / self.low), (self.open / Delay(self.open, 1)))
        return Sum(part, 20)

    def gtja094(self):
        cond1 = (self.close > Delay(self.close, 1))
        cond2 = (self.close < Delay(self.close, 1))
        cond3 = (self.close == Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond1] = self.volume
        part[cond2] = -1 * self.volume
        part[cond3] = 0
        return Sum(part, 30)

    def gtja095(self):
        return Std(self.amount, 20)

    def gtja096(self):
        return Sma(Sma((self.close - Tsmin(self.low, 9)) / (Tsmax(self.high, 9) - Tsmin(self.low, 9)) * 100, 3, 1), 3,
                   1)

    def gtja097(self):
        return Std(self.volume, 10)

    def gtja100(self):
        return Std(self.volume, 20)

    def gtja101(self):
        rank1 = Rank(Corr(self.close, Sum(Mean(self.volume, 30), 37), 15))
        rank2 = Rank(Corr(Rank(((self.high * 0.1) + (self.vwap * 0.9))), Rank(self.volume), 11))
        cond = (rank1 < rank2)
        part = self.close.copy(deep=True)
        part[cond] = 1
        part[~cond] = 0
        return part

    def gtja102(self):
        return Sma(Max(self.volume - Delay(self.volume, 1), 0), 6, 1) / Sma(Abs(self.volume - Delay(self.volume, 1)), 6,
                                                                            1) * 100

    def gtja103(self):
        return ((20 - Lowday(self.low, 20)) / 20) * 100

    def gtja106(self):
        return self.close - Delay(self.close, 20)

    def gtja108(self):
        return (Rank((self.high - Tsmin(self.high, 2))) ** Rank(Corr(self.vwap, (Mean(self.volume, 120)), 6))) * -1

    def gtja109(self):
        return Sma(self.high - self.low, 10, 2) / Sma(Sma(self.high - self.low, 10, 2), 10, 2)

    def gtja110(self):
        return Sum(Max(self.high - Delay(self.close, 1), 0), 20) / Sum(Max(Delay(self.close, 1) - self.low, 0),
                                                                       20) * 100

    def gtja111(self):
        return Sma(self.volume * ((self.close - self.low) - (self.high - self.close)) / (self.high - self.low), 11,
                   2) - Sma(self.volume * ((self.close - self.low) - (self.high - self.close)) / (self.high - self.low),
                            4, 2)

    def gtja112(self):
        cond = (self.close - Delay(self.close, 1) > 0)
        part1 = self.close.copy(deep=True)
        part1[cond] = self.close - Delay(self.close, 1)
        part1[~cond] = 0
        part2 = self.close.copy(deep=True)
        part2[~cond] = Abs(self.close - Delay(self.close, 1))
        part2[cond] = 0
        return (Sum(part1, 12) - Sum(part2, 12)) / (Sum(part1, 12) + Sum(part2, 12)) * 100

    def gtja115(self):
        return (Rank(Corr(((self.high * 0.9) + (self.close * 0.1)), Mean(self.volume, 30), 10)) ** Rank(
            Corr(Tsrank(((self.high + self.low) / 2), 4), Tsrank(self.volume, 10), 7)))

    def gtja116(self):
        return Regbeta(self.close, Sequence(20))

    def gtja118(self):
        return Sum(self.high - self.open, 20) / Sum(self.open - self.low, 20) * 100

    def gtja119(self):
        return (Rank(Decaylinear(Corr(self.vwap, Sum(Mean(self.volume, 5), 26), 5), 7)) - Rank(
            Decaylinear(Tsrank(Tsmin(Corr(Rank(self.open), Rank(Mean(self.volume, 15)), 21), 9), 7), 8)))

    def gtja121(self):
        return ((Rank((self.vwap - Tsmin(self.vwap, 12))) ** Tsrank(
            Corr(Tsrank(self.vwap, 20), Tsrank(Mean(self.volume, 60), 2), 18), 3)) * -1)

    def gtja122(self):
        return (Sma(Sma(Sma(Log(self.close), 13, 2), 13, 2), 13, 2) - Delay(
            Sma(Sma(Sma(Log(self.close), 13, 2), 13, 2), 13, 2), 1)) / Delay(
            Sma(Sma(Sma(Log(self.close), 13, 2), 13, 2), 13, 2), 1)

    def gtja123(self):
        A = Rank(Corr(Sum(((self.high + self.low) / 2), 20), Sum(Mean(self.volume, 60), 20), 9))
        B = Rank(Corr(self.low, self.volume, 6))
        cond = (A < B)
        part = self.close.copy(deep=True)
        part[cond] = -1
        part[~cond] = 0
        return part

    def gtja124(self):
        return (self.close - self.vwap) / Decaylinear(Rank(Tsmax(self.close, 30)), 2)

    def gtja125(self):
        return (Rank(Decaylinear(Corr(self.vwap, Mean(self.volume, 80), 17), 20)) / Rank(
            Decaylinear(DeltaRate(((self.close * 0.5) + (self.vwap * 0.5)), 3), 16)))

    def gtja126(self):
        return (self.high + self.low) / self.close

    def gtja127(self):
        return (Mean((100 * (self.close - Tsmax(self.close, 12)) / (Tsmax(self.close, 12))) ** 2, 12)) ** (1 / 2)

    def gtja128(self):
        A = (self.high + self.low + self.close) / 3
        cond = (A > Delay(A, 1))
        part1 = self.close.copy(deep=True)
        part1[cond] = A * self.volume
        part1[~cond] = 0
        part2 = self.close.copy(deep=True)
        part2[~cond] = A * self.volume
        part2[cond] = 0
        return 100 - (100 / (1 + Sum(part1, 14) / Sum(part2, 14)))

    def gtja129(self):
        cond = ((self.close - Delay(self.close, 1)) < 0)
        part = self.close.copy(deep=True)
        part[cond] = Abs(self.close - Delay(self.close, 1))
        part[~cond] = 0
        return Sum(part, 12)

    def gtja130(self):
        return (Rank(Decaylinear(Corr(((self.high + self.low) / 2), Mean(self.volume, 40), 9), 10)) / Rank(
            Decaylinear(Corr(Rank(self.vwap), Rank(self.volume), 7), 3)))

    def gtja131(self):
        return Rank(DeltaRate(self.vwap, 1)) ** Tsrank(Corr(self.close, Mean(self.volume, 50), 18), 18)

    def gtja132(self):
        return Mean(self.amount, 20)

    def gtja133(self):
        return ((20 - Highday(self.high, 20)) / 20) * 100 - ((20 - Lowday(self.low, 20)) / 20) * 100

    def gtja134(self):
        return (self.close - Delay(self.close, 12)) / Delay(self.close, 12) * self.volume

    def gtja135(self):
        return Sma(Delay(self.close / Delay(self.close, 20), 1), 20, 1)

    def gtja137(self):
        A = Abs(self.high - Delay(self.close, 1))
        B = Abs(self.low - Delay(self.close, 1))
        C = Abs(self.high - Delay(self.low, 1))
        D = Abs(Delay(self.close, 1) - Delay(self.open, 1))
        cond1 = ((A > B) & (A > C))
        cond2 = ((B > C) & (B > A))
        cond3 = ((C >= A) & (C >= B))
        part0 = 16 * (self.close + (self.close - self.open) / 2 - Delay(self.open, 1))
        part1 = self.close.copy(deep=True)
        part1[cond1] = A + B / 2 + D / 4
        part1[~cond1] = 0
        part1[cond2] = B + A / 2 + D / 4
        part1[cond3] = C + D / 4
        return part0 / part1 * Max(A, B)

    def gtja138(self):
        return ((Rank(Decaylinear(DeltaRate((self.low * 0.7 + self.vwap * 0.3), 3), 20)) - Tsrank(
            Decaylinear(Tsrank(Corr(Tsrank(self.low, 8), Tsrank(Mean(self.volume, 60), 17), 5), 19), 16), 7)) * -1)

    def gtja140(self):
        return Min(Rank(Decaylinear(((self.open + self.low) - (self.high + self.close)), 8)),
                   Tsrank(Decaylinear(Corr(Tsrank(self.close, 8), Tsrank(Mean(self.volume, 60), 20), 8), 7), 3))

    def gtja141(self):
        return Rank(Corr(self.high, (Mean(self.volume, 15)), 9)) * -1

    def gtja144(self):
        cond = (self.close < Delay(self.close, 1))
        part1 = Abs(self.close / Delay(self.close, 1) - 1) / DeltaRate(self.amount, 1)
        return Sumif(part1, 20, cond) / Count(cond, 20)

    def gtja145(self):
        return (Mean(self.volume, 9) - Mean(self.volume, 26)) / Mean(self.volume, 12) * 100

    def gtja146(self):
        return Mean((self.close - Delay(self.close, 1)) / Delay(self.close, 1) - Sma(
            (self.close - Delay(self.close, 1)) / Delay(self.close, 1), 61, 2), 20) * (
                (self.close - Delay(self.close, 1)) / Delay(self.close, 1) - Sma(
            (self.close - Delay(self.close, 1)) / Delay(self.close, 1), 61, 2)) / Sma(((self.close - Delay(
            self.close, 1)) / Delay(self.close, 1) - ((self.close - Delay(self.close, 1)) / Delay(self.close, 1) - Sma(
            (self.close - Delay(self.close, 1)) / Delay(self.close, 1), 61, 2))) ** 2, 61, 2)

    def gtja147(self):
        return Regbeta(Mean(self.close, 12), Sequence(12))

    def gtja148(self):
        cond = (Rank(Corr(self.open, Sum(Mean(self.volume, 60), 9), 6)) < Rank((self.open - Tsmin(self.open, 14))))
        part = self.close.copy(deep=True)
        part[cond] = -1
        part[~cond] = 0
        return part

    def gtja150(self):
        return (self.close + self.high + self.low) / 3 * self.volume

    def gtja151(self):
        return Sma(self.close - Delay(self.close, 20), 20, 1)

    def gtja152(self):
        return Sma(Mean(Delay(Sma(Delay(self.close / Delay(self.close, 9), 1), 9, 1), 1), 12) - Mean(
            Delay(Sma(Delay(self.close / Delay(self.close, 9), 1), 9, 1), 1), 26), 9, 1)

    def gtja153(self):
        return (Mean(self.close, 3) + Mean(self.close, 6) + Mean(self.close, 12) + Mean(self.close, 24)) / self.close

    def gtja154(self):
        cond = ((self.vwap - Tsmin(self.vwap, 16)) < (Corr(self.vwap, Mean(self.volume, 180), 18)))
        part = self.close.copy(deep=True)
        part[cond] = 1
        part[~cond] = 0
        return part

    def gtja155(self):
        return (Sma(self.volume, 13, 2) - Sma(self.volume, 27, 2) - Sma(
            Sma(self.volume, 13, 2) - Sma(self.volume, 27, 2), 10, 2)) / self.volume

    def gtja156(self):
        return (Max(Rank(Decaylinear(Delta(self.vwap, 5), 3)), Rank(Decaylinear(
            ((Delta(((self.open * 0.15) + (self.low * 0.85)), 2) / ((self.open * 0.15) + (self.low * 0.85))) * -1),
            3))) * -1)

    def gtja157(self):
        return (Tsmin(Prod(Rank(Rank(Log(Sum(Tsmin(Rank(Rank((-1 * Rank(Delta((self.close - 1), 5))))), 2), 1)))), 1),
                      5) + Tsrank(Delay((-1 * self.returns), 6), 5))

    def gtja158(self):
        return ((self.high - Sma(self.close, 15, 2)) - (self.low - Sma(self.close, 15, 2))) / self.close

    def gtja159(self):
        return ((self.close - Sum(Min(self.low, Delay(self.close, 1)), 6)) / Sum(
            Max(self.high, Delay(self.close, 1)) - Min(self.low, Delay(self.close, 1)), 6) * 12 * 24 + (
                        self.close - Sum(Min(self.low, Delay(self.close, 1)), 12)) / Sum(
            Max(self.high, Delay(self.close, 1)) - Min(self.low, Delay(self.close, 1)), 12) * 6 * 24 + (
                        self.close - Sum(Min(self.low, Delay(self.close, 1)), 24)) / Sum(
            Max(self.high, Delay(self.close, 1)) - Min(self.low, Delay(self.close, 1)), 24) * 6 * 24) * 100 / (
                6 * 12 + 6 * 24 + 12 * 24)

    def gtja160(self):
        cond = (self.close <= Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond] = Std(self.close, 20) / Delay(self.close, 20)
        part[~cond] = 0
        return Sma(part, 20, 1)

    def gtja161(self):
        return Mean(Max(Max((self.high - self.low), Abs(Delay(self.close, 1) - self.high)),
                        Abs(Delay(self.close, 1) - self.low)), 12)

    def gtja162(self):
        return (Sma(Max(self.close - Delay(self.close, 1), 0), 12, 1) / Sma(Abs(self.close - Delay(self.close, 1)), 12,
                                                                            1) * 100 - Tsmin(
            Sma(Max(self.close - Delay(self.close, 1), 0), 12, 1) / Sma(Abs(self.close - Delay(self.close, 1)), 12,
                                                                        1) * 100, 12)) / (
                Sma(Sma(Max(self.close - Delay(self.close, 1), 0), 12, 1) / Sma(
                    Abs(self.close - Delay(self.close, 1)), 12, 1) * 100, 12, 1) - Tsmin(
            Sma(Max(self.close - Delay(self.close, 1), 0), 12, 1) / Sma(Abs(self.close - Delay(self.close, 1)), 12,
                                                                        1) * 100, 12))

    def gtja164(self):
        cond = (self.close > Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond] = 1 / (self.close - Delay(self.close, 1))
        part[~cond] = 1
        return Sma((part - Tsmin(part, 12)) / (self.high - self.low) * 100, 13, 2)

    def gtja165(self):
        p1 = Rowmax(Sum(self.close - Mean(self.close, 48), 48))
        p2 = Rowmin(Sum(self.close - Mean(self.close, 48), 48))
        p3 = Std(self.close, 48)
        return -1 * (1 / p3.div(p2, axis=0)).sub(p1, axis=0)

    def gtja166(self):
        p1 = -20 * (20 - 1) ** 1.5 * Sum(
            self.close / Delay(self.close, 1) - 1 - Mean(self.close / Delay(self.close, 1) - 1, 20), 20)
        p2 = ((20 - 1) * (20 - 2) * (Sum(Mean(self.close / Delay(self.close, 1), 20) ** 2, 20)) ** 1.5)
        return p1 / p2

    def gtja167(self):
        cond = (self.close > Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond] = self.close - Delay(self.close, 1)
        part[~cond] = 0
        return Sum(part, 12)

    def gtja168(self):
        return -1 * self.volume / Mean(self.volume, 20)

    def gtja169(self):
        return Sma(Mean(Delay(Sma(self.close - Delay(self.close, 1), 9, 1), 1), 12) - Mean(
            Delay(Sma(self.close - Delay(self.close, 1), 9, 1), 1), 26), 10, 1)

    def gtja170(self):
        return ((((Rank((1 / self.close)) * self.volume) / Mean(self.volume, 20)) * (
                (self.high * Rank((self.high - self.close))) / (Sum(self.high, 5) / 5))) - Rank(
            (self.vwap - Delay(self.vwap, 5))))

    def gtja171(self):
        return (-1 * ((self.low - self.close) * (self.open ** 5))) / ((self.close - self.high) * (self.close ** 5))

    def gtja172(self):
        TR = Max(Max(self.high - self.low, Abs(self.high - Delay(self.close, 1))), Abs(self.low - Delay(self.close, 1)))
        HD = self.high - Delay(self.high, 1)
        LD = Delay(self.low, 1) - self.low
        cond1 = ((LD > 0) & (LD > HD))
        cond2 = ((HD > 0) & (HD > LD))
        part1 = self.close.copy(deep=True)
        part1[cond1] = LD
        part1[~cond1] = 0
        part2 = self.close.copy(deep=True)
        part2[cond2] = HD
        part2[~cond2] = 0
        return Mean(Abs(Sum(part1, 14) * 100 / Sum(TR, 14) - Sum(part2, 14) * 100 / Sum(TR, 14)) / (
                Sum(part1, 14) * 100 / Sum(TR, 14) + Sum(part2, 14) * 100 / Sum(TR, 14)) * 100, 6)

    def gtja173(self):
        return 3 * Sma(self.close, 13, 2) - 2 * Sma(Sma(self.close, 13, 2), 13, 2) + Sma(
            Sma(Sma(Log(self.close), 13, 2), 13, 2), 13, 2)

    def gtja174(self):
        cond = (self.close > Delay(self.close, 1))
        part = self.close.copy(deep=True)
        part[cond] = Std(self.close, 20)
        part[~cond] = 0
        return Sma(part, 20, 1)

    def gtja175(self):
        return Mean(Max(Max((self.high - self.low), Abs(Delay(self.close, 1) - self.high)),
                        Abs(Delay(self.close, 1) - self.low)), 6)

    def gtja176(self):
        return Corr(Rank(((self.close - Tsmin(self.low, 12)) / (Tsmax(self.high, 12) - Tsmin(self.low, 12)))),
                    Rank(self.volume), 6)

    def gtja177(self):
        return ((20 - Highday(self.high, 20)) / 20) * 100

    def gtja178(self):
        return (self.close - Delay(self.close, 1)) / Delay(self.close, 1) * self.volume

    def gtja179(self):
        return Rank(Corr(self.vwap, self.volume, 4)) * Rank(Corr(Rank(self.low), Rank(Mean(self.volume, 50)), 12))

    def gtja180(self):
        cond = (Mean(self.volume, 20) < self.volume)
        part = self.close.copy(deep=True)
        part[cond] = (-1 * Tsrank(Abs(Delta(self.close, 7)), 60)) * Sign(Delta(self.close, 7))
        part[~cond] = -1 * self.volume
        return part

    def gtja181(self):
        return Sum(((self.close / Delay(self.close, 1) - 1) - Mean((self.close / Delay(self.close, 1) - 1), 20)) - (
                self.benchmark_close - Mean(self.benchmark_close, 20)) ** 2, 20) / Sum(
            ((self.benchmark_close - Mean(self.benchmark_close, 20)) ** 3), 20)

    def gtja182(self):
        return Count((((self.close > self.open) & (self.benchmark_close > self.benchmark_open)) | (
                (self.close < self.open) & (self.benchmark_close < self.benchmark_open))), 20) / 20

    def gtja183(self):
        p1 = Rowmax(Sum(self.close - Mean(self.close, 24), 24))
        p2 = Rowmin(Sum(self.close - Mean(self.close, 24), 24))
        p3 = Std(self.close, 24)
        return -1 * (1 / p3.div(p2, axis=0)).sub(p1, axis=0)

    def gtja185(self):
        return Rank((-1 * ((1 - (self.open / self.close)) ** 2)))

    def gtja186(self):
        TR = Max(Max(self.high - self.low, Abs(self.high - Delay(self.close, 1))), Abs(self.low - Delay(self.close, 1)))
        HD = self.high - Delay(self.high, 1)
        LD = Delay(self.low, 1) - self.low
        cond1 = ((LD > 0) & (LD > HD))
        cond2 = ((HD > 0) & (HD > LD))
        part1 = self.close.copy(deep=True)
        part1[cond1] = LD
        part1[~cond1] = 0
        part2 = self.close.copy(deep=True)
        part2[cond2] = HD
        part2[~cond2] = 0
        return (Mean(Abs(Sum(part1, 14) * 100 / Sum(TR, 14) - Sum(part2, 14) * 100 / Sum(TR, 14)) / (
                Sum(part1, 14) * 100 / Sum(TR, 14) + Sum(part2, 14) * 100 / Sum(TR, 14)) * 100, 6) + Delay(Mean(
            Abs(Sum(part1, 14) * 100 / Sum(TR, 14) - Sum(part2, 14) * 100 / Sum(TR, 14)) / (
                    Sum(part1, 14) * 100 / Sum(TR, 14) + Sum(part2, 14) * 100 / Sum(TR, 14)) * 100, 6), 6)) / 2

    def gtja187(self):
        cond = (self.open <= Delay(self.open, 1))
        part = self.close.copy(deep=True)
        part[cond] = 0
        part[~cond] = Max((self.high - self.open), (self.open - Delay(self.open, 1)))
        return Sum(part, 20)

    def gtja188(self):
        return ((self.high - self.low - Sma(self.high - self.low, 11, 2)) / Sma(self.high - self.low, 11, 2)) * 100

    def gtja189(self):
        return Mean(Abs(self.close - Mean(self.close, 6)), 6)

    def gtja191(self):
        return (Corr(Mean(self.volume, 20), self.low, 5) + ((self.high + self.low) / 2)) / self.close


if __name__ == '__main__':
    Alphas191.generate_alphas('20120101', '20120331')
