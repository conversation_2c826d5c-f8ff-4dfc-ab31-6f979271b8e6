#!/bin/bash
py_exec="/home/<USER>/miniconda3/envs/ai_retry/bin/python"

n=0

while true; do
    # 计算递增的数字
    num_inc=$((340 + n))
    # 计算递减的数字
    num_dec=$((340 - n))

    # 当 n 为 0 时，只执行 num_inc
    if [ $n -eq 0 ]; then
        if [ $num_inc -le 370 ]; then
            $py_exec ../stratergy_runner.py -r -c $num_inc --fc_res fc_csi698
        fi
    else
        # 交替执行递增和递减的数字
        if [ $num_inc -le 370 ]; then
            $py_exec ../stratergy_runner.py -r -c $num_inc --fc_res fc_csi698
        fi
        if [ $num_dec -ge 290 ]; then
            $py_exec ../stratergy_runner.py -r -c $num_dec --fc_res fc_csi698
        fi
    fi

    # 检查是否达到结束条件
    if [ $num_inc -gt 370 ] && [ $num_dec -lt 290 ]; then
        break
    fi

    n=$((n + 1))
done

$py_exec ../tools/after_fc_select.py fc_csi698.csv
$py_exec ../stratergy_runner.py --run_select_alpha
