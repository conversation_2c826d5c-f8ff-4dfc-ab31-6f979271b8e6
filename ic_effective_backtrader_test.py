import os

import alphalens
import backtrader as bt
import fire
import numpy as np
import pandas as pd
import qlib
from backtrader_plotting import <PERSON><PERSON><PERSON>
from backtrader_plotting.schemes import Tradimo
from qlib.constant import REG_CN
from qlib.data import D

from util import prepare_cerebro


def get_stock_data(instrument_code, start_time, end_time):
    instruments = D.list_instruments(D.instruments(market=instrument_code))
    data = []
    for instrument in instruments:
        df = D.features([instrument], ["$close", "$volume", "$high", "$low", "$open",
                                       "$adjclose", '$amount', '$change', '$factor', '$vwap'],
                        start_time=start_time, end_time=end_time, freq="day")
        df.rename(columns={"$close": "close",
                           "$volume": "volume",
                           "$high": "high",
                           "$low": "low", '$open': "open", '$adjclose': "adjclose", "$amount": "amount",
                           '$change': "change", '$factor': "factor", '$vwap': "vwap"}, inplace=True)
        df.dropna(inplace=True)
        data.append(df)

    all_data = pd.concat(data, axis=0)
    return all_data


class ICEffactiveFactor:
    def __init__(self, ic_rolling, ic_gate, threashold_torlerance, ic_index):
        self.ic_rolling = ic_rolling
        self.ic_gate = ic_gate
        self.threashold_torlerance = threashold_torlerance
        self.ic_index = ic_index
        self.ic_threathold = 1

    def update(self, cur_date, is_empty):
        cur_ic_rolling = self.ic_rolling[self.ic_rolling.index == cur_date]
        if cur_ic_rolling.empty:
            return False
        cur_ic_rolling = cur_ic_rolling.iloc[0][self.ic_index]
        if np.isnan(cur_ic_rolling):
            return False

        if (cur_ic_rolling < self.ic_gate) or (self.ic_threathold - cur_ic_rolling > self.threashold_torlerance):
            self.ic_threathold = cur_ic_rolling
            return False
        if is_empty:
            if cur_ic_rolling < self.ic_threathold + self.threashold_torlerance:
                return False
        self.ic_threathold = max(self.ic_threathold, cur_ic_rolling)
        return True


class MultiIndexStratergy(bt.Strategy):
    params = (
        ('printlog', True),
        ('features', None),
        ('ic_rollings', None),
        ('ic_gate', 0.02),
        ('num_stocks', 20),
        ('sell_rank_threshold', 40),
        ('threashold_torlerance', 0.01),
        ('max_down', 0.1),
        ('ic_index', '1D')
    )

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.date(0)
        if self.p.printlog:
            print('%s, %s' % (dt.isoformat(), txt))

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            return
        sec_code = order.data._name
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log('BUY EXECUTED %s, Price: %.2f, Cost: %.2f, Comm %.2f' %
                         (sec_code,
                          order.executed.price,
                          order.executed.value,
                          order.executed.comm))
            elif order.issell():
                self.log('SELL EXECUTED %s, Price: %.2f, Cost: %.2f, Comm %.2f' %
                         (sec_code,
                          order.executed.price,
                          order.executed.value,
                          order.executed.comm))
        elif order.status in [order.Canceled, order.Rejected, order.Margin]:
            if sec_code in self.buy_pre and order.isbuy():
                del self.buy_pre[sec_code]
            self.log('Order Canceled/Rejected %s:%s ' % (sec_code, order.isbuy()))

    def notify_trade(self, trade):
        if not trade.isclosed:
            return
        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' % (trade.pnl, trade.pnlcomm))

    def __init__(self):
        super().__init__()
        self.features = self.p.features
        if self.p.ic_rollings is not None:
            self.ic_effactive_indicator = [ICEffactiveFactor(ic_rolling=i,
                                                             ic_gate=self.p.ic_gate,
                                                             ic_index=self.p.ic_index,
                                                             threashold_torlerance=self.p.threashold_torlerance) for i
                                           in
                                           self.p.ic_rollings]
        else:
            self.ic_effactive_indicator = None
        self.num_stocks = self.p.num_stocks
        self.buy_pre = {}
        self.max_down = self.p.max_down
        self.sell_rank_threshold = self.p.sell_rank_threshold

    def close_all(self):
        for sec_code, info in self.buy_pre.items():
            self.close(data=self.getdatabyname(sec_code))
        self.buy_pre.clear()

    def check_sells(self, top_stocks):
        buy_pre_back = self.buy_pre.copy()
        for sec_code, info in buy_pre_back.items():
            price = info[0]
            change = (self.getdatabyname(sec_code).close[0] - price) / price
            if change < -self.max_down or sec_code not in top_stocks:
                self.close(data=self.getdatabyname(sec_code))
                self.buy_pre.pop(sec_code)
        return self.num_stocks - len(buy_pre_back)

    def next(self):
        cur_date = pd.to_datetime(self.datas[0].datetime.date(0))
        is_empty = (len(self.buy_pre) == 0)
        if self.ic_effactive_indicator is not None:
            ic_effective_infos = [i.update(cur_date, is_empty) for i in self.ic_effactive_indicator]
            if not any(ic_effective_infos):
                self.close_all()
                return
            feature = pd.concat([self.features[i][self.features[i].index.get_level_values(0) == cur_date] for i, info in
                                 enumerate(ic_effective_infos) if info], axis=1)
        else:
            feature = pd.concat([f[f.index.get_level_values(0) == cur_date] for f in self.features], axis=1)
        feature_rank = feature.rank(axis=0, ascending=True)
        top_stocks = feature_rank.sum(axis=1).sort_values(ascending=False).head(
            self.sell_rank_threshold).index.get_level_values(1).tolist()
        empty_count = self.check_sells(top_stocks)
        if empty_count <= 0:
            return
        avail_cash = self.broker.get_cash()
        each_cash = self.broker.get_value() / self.num_stocks
        for i in range(0, len(top_stocks)):
            if avail_cash < each_cash * 0.8:
                break
            code = top_stocks[i]
            if code in self.buy_pre or code not in self.env.datasbyname.keys():
                i += 1
                continue
            price = self.getdatabyname(code).close[0]
            self.buy_pre[code] = [price, cur_date]
            buy_cash = min(avail_cash, each_cash) * 0.98
            avail_cash -= buy_cash
            self.buy(data=self.getdatabyname(code), size=int(buy_cash / price))
            if len(self.buy_pre) == self.num_stocks:
                break


def draw(cerebro, stats):
    for dt in cerebro.datas:
        dt.plotinfo.plot = False
    stats[0].trade_data = stats[0].p.trade_data = 0
    bokeh = Bokeh(style='bar', plot_mode='single', scheme=Tradimo())
    cerebro.plot(bokeh)


def dump_stats_cerebro(stats):
    msg = str(stats[0].analyzers.annualreturn.get_analysis()) + '\r\n'
    msg += str(stats[0].analyzers.sharperatio.get_analysis()) + '\r\n'
    msg += str(stats[0].analyzers.drawdown.get_analysis()) + '\r\n'
    print(msg)


def alpha_main(start, end, instrument, alpha_path, *alphas, **kwargs):
    features = [pd.read_pickle(f'{alpha_path}/{i}.pkl') for i in alphas]
    start_prev_month = pd.to_datetime(start) - pd.DateOffset(months=1)
    stock_data = get_stock_data(instrument, start_prev_month, end)
    new_index_level_2 = stock_data.index.get_level_values(0).map(lambda x: x[2:])
    new_index = list(zip(new_index_level_2, stock_data.index.get_level_values(1)))
    stock_data.index = pd.MultiIndex.from_tuples(new_index, names=stock_data.index.names)
    features = [i[(i.index.get_level_values(0) >= start_prev_month) &
                  (i.index.get_level_values(0) <= end)] for i in features]
    return back_trade(start, end, features, stock_data, **kwargs)


def back_trade(start, end, features, stock_data, enable_ic, **kwargs):
    close = stock_data[['close']]
    close = close.shift(1)
    close = close.pivot_table(values='close', index='datetime', columns='instrument')
    if enable_ic:
        factors = [alphalens.utils.get_clean_factor_and_forward_returns(i, close,
                                                                        bins=None,
                                                                        periods=(1, 5, 10), quantiles=5,
                                                                        max_loss=0.50) for i in features]
        ics = [alphalens.performance.factor_information_coefficient(i) for i in factors]
        ic_rollings = [ic.rolling(window=10).mean() for ic in ics]
    else:
        ic_rollings = None
    stock_data = stock_data.swaplevel(0, 1)
    cerebro = bt.Cerebro()
    start = pd.to_datetime(start)
    end = pd.to_datetime(end)
    for sec_code in stock_data.index.get_level_values(1).unique():
        stock_df = stock_data[stock_data.index.get_level_values(1) == sec_code].copy()
        stock_df.reset_index(inplace=True)
        stock_df.set_index('datetime', inplace=True)
        min_date = stock_df.index.min()
        if min_date > start + pd.DateOffset(days=7):
            continue
        max_date = stock_df.index.max()
        if max_date < end - pd.DateOffset(days=7):
            continue
        stock_df = stock_df[['open', 'high', 'low', 'close', 'volume']]
        stock_df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'},
                        inplace=True)
        stock_df.index = pd.to_datetime(stock_df.index)
        stock_df = stock_df[stock_df.index >= start]
        data = bt.feeds.PandasData(dataname=stock_df)
        cerebro.adddata(data, name=sec_code)
    cerebro.addstrategy(MultiIndexStratergy,
                        features=features,
                        ic_rollings=ic_rollings, **kwargs)
    prepare_cerebro(cerebro)
    stats = cerebro.run(optreturn=True)
    dump_stats_cerebro(stats)
    return cerebro.broker.getvalue() / 10000000.0


def backtrader_for_all_alphas(start, end, instrument, alpha_path='alpha', enable_ic=True, *selected):
    res = {}
    if selected is None or len(selected) == 0:
        for i in os.listdir(alpha_path):
            if i.endswith('.pkl'):
                alpha_name = i.split('.')[0]
                print('begin process ', alpha_name)
                ret = alpha_main(start, end, instrument, alpha_path, alpha_name, enable_ic=enable_ic)
                res[alpha_name] = ret
                print(f'{alpha_name}: {ret}')

                df = pd.DataFrame(res.items(), columns=['alpha', 'annual_return'])
                df.to_csv('annual_return.csv', index=False)
    else:
        ret = alpha_main(start, end, instrument, alpha_path, *selected, enable_ic=enable_ic)
        print('ret ', ret)


def backtrader_test_main(start, end, instrument='csi300', alpha_path='alpha', enable_ic=False, *selected):
    qlib.init(provider_uri="./qlib_bin", region=REG_CN)
    backtrader_for_all_alphas(start, end, instrument, alpha_path, enable_ic, *selected)


if __name__ == '__main__':
    fire.Fire(backtrader_test_main)
