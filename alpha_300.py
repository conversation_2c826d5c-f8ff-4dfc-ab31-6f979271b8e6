import numpy as np
import pandas as pd
from alphabase import Alphas  # 请确保 alphabase 模块中定义了 Alphas 基类


def correlation_with_linear_range(df, window=10):
    """
    对 df 的每一列，在滚动窗口 window 内，与一个线性序列 [0,1,...,window-1] 做相关系数。
    返回一个与 df 同 shape 的结果 DataFrame。
    """
    def corr_func(window_values):
        return np.corrcoef(window_values, range(window))[0, 1]

    return df.rolling(window).apply(corr_func, raw=True)


class ClaudeAlphas(Alphas):
    def __init__(self, df_data):
        super().__init__(df_data)
        self.open = df_data['open']
        self.high = df_data['high']
        self.low = df_data['low']
        self.close = df_data['close']
        self.volume = df_data['volume']
        self.returns = df_data['change']
        self.vwap = df_data['vwap']
        self.amount = df_data['amount']
        # 如有基准数据，请确保存在 benchmark_close 等字段
        if 'benchmark_close' in df_data.columns:
            self.benchmark_close = df_data['benchmark_close']
        else:
            self.benchmark_close = None

    # ---------------------------
    # 辅助函数（部分来源于之前代码）
    # ---------------------------

    def sma(self, series, window):
        return series.rolling(window, min_periods=max(1, window // 2)).mean()

    def ema(self, series, window):
        return series.ewm(span=window, adjust=False).mean()

    def stddev(self, series, window):
        return series.rolling(window, min_periods=max(1, window // 2)).std()

    def rsi(self, series, window):
        delta = series.diff()
        up = delta.clip(lower=0)
        down = -delta.clip(upper=0)
        avg_gain = up.rolling(window, min_periods=window).mean()
        avg_loss = down.rolling(window, min_periods=window).mean()
        rs = avg_gain / (avg_loss + 1e-6)
        return 100 - (100 / (1 + rs))

    def momentum(self, series, window):
        return series - series.shift(window)

    def atr(self, window):
        tr1 = self.high - self.low
        tr2 = (self.high - self.close.shift(1)).abs()
        tr3 = (self.low - self.close.shift(1)).abs()
        tr = pd.DataFrame(np.maximum(np.maximum(tr1, tr2), tr3),
                          index=self.high.index, columns=self.high.columns)
        return tr.rolling(window, min_periods=window).mean()

    def chaikin_oscillator(self):
        adl = ((self.close - self.low) - (self.high - self.close)) / (self.high - self.low + 1e-6) * self.volume
        adl = adl.cumsum()
        return self.ema(adl, 3) - self.ema(adl, 10)

    def obv(self):
        direction = np.sign(self.close.diff())
        return (direction * self.volume).cumsum()

    def rolling_rank(self, data, window=250):
        if isinstance(data, pd.DataFrame):
            return data.apply(lambda col: col.rolling(window, min_periods=window)
                              .apply(lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False))
        else:
            return data.rolling(window, min_periods=window) \
                .apply(lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False)

    # 以下为 alpha_120 中所用的辅助函数
    def Delta(self, sr, period):
        return sr.diff(period)

    def Correlation(self, x, y, window):
        return x.rolling(window).corr(y)

    def Mean(self, sr, window):
        return sr.rolling(window).mean()

    def Maximum(self, sr, window):
        return sr.rolling(window).max()

    def Minimum(self, sr, window):
        return sr.rolling(window).min()

    def Decay_linear(self, sr, window):
        weights = np.arange(1, window + 1)
        weights = weights / weights.sum()
        return sr.rolling(window).apply(lambda x: np.sum(weights * x), raw=True)

    def Rank(self, sr):
        return sr.rank(pct=True)

    def calculate_RSI(self, period=14):
        delta = self.close.diff()
        gain = delta.where(delta > 0, 0).rolling(window=period, min_periods=period).mean()
        loss = (-delta).where(delta < 0, 0).rolling(window=period, min_periods=period).mean()
        rs = gain / (loss + 1e-6)
        return 100 - (100 / (1 + rs))

    def calculate_MACD(self, fast=12, slow=26, signal=9):
        exp1 = self.close.ewm(span=fast, adjust=False).mean()
        exp2 = self.close.ewm(span=slow, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal, adjust=False).mean()
        return macd - signal_line

    def calculate_BB(self, period=20, std_dev=2):
        ma = self.close.rolling(window=period).mean()
        std = self.close.rolling(window=period).std()
        upper = ma + std_dev * std
        lower = ma - std_dev * std
        return (self.close - lower) / (upper - lower + 1e-6)

    # ---------------------------
    # 以下为原先从 alpha_300（新增因子）中提取的因子，分组归类后重命名为 claude001～claude085
    # Group 77: Normalized EMA Crossover Factors
    def claude001(self):
        return (self.ema(self.close, 5) - self.ema(self.close, 20)) / (self.ema(self.close, 20) + 1e-6)

    def claude002(self):
        return (self.ema(self.close, 5) - self.ema(self.close, 30)) / (self.ema(self.close, 30) + 1e-6)

    def claude003(self):
        return (self.ema(self.close, 5) - self.ema(self.close, 50)) / (self.ema(self.close, 50) + 1e-6)

    def claude004(self):
        return (self.ema(self.close, 10) - self.ema(self.close, 20)) / (self.ema(self.close, 20) + 1e-6)

    def claude005(self):
        return (self.ema(self.close, 10) - self.ema(self.close, 30)) / (self.ema(self.close, 30) + 1e-6)

    def claude006(self):
        return (self.ema(self.close, 10) - self.ema(self.close, 50)) / (self.ema(self.close, 50) + 1e-6)

    def claude007(self):
        return (self.ema(self.close, 15) - self.ema(self.close, 20)) / (self.ema(self.close, 20) + 1e-6)

    def claude008(self):
        return (self.ema(self.close, 15) - self.ema(self.close, 30)) / (self.ema(self.close, 30) + 1e-6)

    def claude009(self):
        return (self.ema(self.close, 15) - self.ema(self.close, 50)) / (self.ema(self.close, 50) + 1e-6)

    def claude010(self):
        return (self.ema(self.close, 20) - self.ema(self.close, 50)) / (self.ema(self.close, 50) + 1e-6)

    # Group 78: RSI Divergence Factors
    def claude011(self):
        return (self.calculate_RSI(5) - self.sma(self.calculate_RSI(5), 5)) / (self.calculate_RSI(5) + 1e-6)

    def claude012(self):
        return (self.calculate_RSI(10) - self.sma(self.calculate_RSI(10), 10)) / (self.calculate_RSI(10) + 1e-6)

    def claude013(self):
        return (self.calculate_RSI(14) - self.sma(self.calculate_RSI(14), 14)) / (self.calculate_RSI(14) + 1e-6)

    def claude014(self):
        return (self.calculate_RSI(20) - self.sma(self.calculate_RSI(20), 20)) / (self.calculate_RSI(20) + 1e-6)

    def claude015(self):
        return self.calculate_RSI(5).diff(1) / (self.calculate_RSI(5) + 1e-6)

    def claude016(self):
        return self.calculate_RSI(10).diff(1) / (self.calculate_RSI(10) + 1e-6)

    def claude017(self):
        return self.calculate_RSI(14).diff(1) / (self.calculate_RSI(14) + 1e-6)

    def claude018(self):
        return self.calculate_RSI(20).diff(1) / (self.calculate_RSI(20) + 1e-6)

    def claude019(self):
        return self.calculate_RSI(5) / (self.calculate_RSI(5).shift(1) + 1e-6) - 1

    def claude020(self):
        return self.calculate_RSI(10) / (self.calculate_RSI(10).shift(1) + 1e-6) - 1

    # Group 79: Bollinger Band Position Factors
    def claude021(self):
        sma10 = self.sma(self.close, 10)
        std10 = self.stddev(self.close, 10)
        lower = sma10 - 2 * std10
        upper = sma10 + 2 * std10
        return (self.close - lower) / (upper - lower + 1e-6)

    def claude022(self):
        sma20 = self.sma(self.close, 20)
        std20 = self.stddev(self.close, 20)
        lower = sma20 - 2 * std20
        upper = sma20 + 2 * std20
        return (self.close - lower) / (upper - lower + 1e-6)

    def claude023(self):
        sma30 = self.sma(self.close, 30)
        std30 = self.stddev(self.close, 30)
        lower = sma30 - 2 * std30
        upper = sma30 + 2 * std30
        return (self.close - lower) / (upper - lower + 1e-6)

    def claude024(self):
        sma50 = self.sma(self.close, 50)
        std50 = self.stddev(self.close, 50)
        lower = sma50 - 2 * std50
        upper = sma50 + 2 * std50
        return (self.close - lower) / (upper - lower + 1e-6)

    def claude025(self):
        return (self.close - self.sma(self.close, 10)) / (2 * self.stddev(self.close, 10) + 1e-6)

    def claude026(self):
        return (self.close - self.sma(self.close, 20)) / (2 * self.stddev(self.close, 20) + 1e-6)

    def claude027(self):
        return (self.close - self.sma(self.close, 30)) / (2 * self.stddev(self.close, 30) + 1e-6)

    def claude028(self):
        return (self.close - self.sma(self.close, 50)) / (2 * self.stddev(self.close, 50) + 1e-6)

    def claude029(self):
        bb = (self.close - (self.sma(self.close, 10) - 2 * self.stddev(self.close, 10))) / (
                    4 * self.stddev(self.close, 10) + 1e-6)
        return bb / (bb.shift(1) + 1e-6) - 1

    def claude030(self):
        bb = (self.close - (self.sma(self.close, 20) - 2 * self.stddev(self.close, 20))) / (
                    4 * self.stddev(self.close, 20) + 1e-6)
        return bb / (bb.shift(1) + 1e-6) - 1

    # Group 80: Price Momentum Acceleration Factors
    def claude031(self):
        return self.close.diff(1).diff(1) / (self.momentum(self.close, 1) + 1e-6)

    def claude032(self):
        return self.close.diff(3).diff(3) / (self.momentum(self.close, 3) + 1e-6)

    def claude033(self):
        return self.close.diff(5).diff(5) / (self.momentum(self.close, 5) + 1e-6)

    def claude034(self):
        return self.close.diff(1).diff(3) / (self.momentum(self.close, 1) + 1e-6)

    def claude035(self):
        return self.close.diff(3).diff(3) / (self.momentum(self.close, 3) + 1e-6)

    def claude036(self):
        return self.close.diff(5).diff(3) / (self.momentum(self.close, 5) + 1e-6)

    def claude037(self):
        return self.close.diff(2) / (self.sma(self.close, 5) + 1e-6)

    def claude038(self):
        return self.close.diff(2) / (self.sma(self.close, 10) + 1e-6)

    def claude039(self):
        return self.close.diff(3) / (self.sma(self.close, 10) + 1e-6)

    def claude040(self):
        return self.close.diff(3) / (self.sma(self.close, 15) + 1e-6)

    # Group 81: ATR Based Factors
    def claude041(self):
        return self.atr(5) / (self.close + 1e-6)

    def claude042(self):
        return self.atr(10) / (self.close + 1e-6)

    def claude043(self):
        return self.atr(14) / (self.close + 1e-6)

    def claude044(self):
        return self.atr(20) / (self.close + 1e-6)

    def claude045(self):
        return self.atr(5).diff(1) / (self.atr(5) + 1e-6)

    def claude046(self):
        return self.atr(10).diff(1) / (self.atr(10) + 1e-6)

    def claude047(self):
        return self.atr(14).diff(1) / (self.atr(14) + 1e-6)

    def claude048(self):
        return self.atr(20).diff(1) / (self.atr(20) + 1e-6)

    def claude049(self):
        return self.atr(10) / (self.stddev(self.close, 10) + 1e-6)

    def claude050(self):
        return self.atr(20) / (self.stddev(self.close, 20) + 1e-6)

    # Group 82: Chaikin Oscillator Divergence Factors
    def claude051(self):
        ch = self.chaikin_oscillator()
        return ch - ch.rolling(5, min_periods=5).mean()

    def claude052(self):
        ch = self.chaikin_oscillator()
        return ch - ch.rolling(10, min_periods=10).mean()

    def claude053(self):
        ch = self.chaikin_oscillator()
        return ch.diff(1)

    def claude054(self):
        ch = self.chaikin_oscillator()
        return ch.diff(3)

    def claude055(self):
        ch = self.chaikin_oscillator()
        return (ch - self.sma(ch, 5)) / (self.sma(ch, 5) + 1e-6)

    def claude056(self):
        ch = self.chaikin_oscillator()
        return (ch - self.sma(ch, 10)) / (self.sma(ch, 10) + 1e-6)

    def claude057(self):
        return self.rolling_rank(self.chaikin_oscillator(), window=250)

    def claude058(self):
        ch = self.chaikin_oscillator()
        return ch.diff(5) / (ch + 1e-6)

    def claude059(self):
        ch = self.chaikin_oscillator()
        return ch.diff(10) / (ch + 1e-6)

    def claude060(self):
        ch = self.chaikin_oscillator()
        return ch.rolling(5, min_periods=5).std() / (self.sma(ch, 5) + 1e-6)

    # Group 83: OBV Based Factors
    def claude061(self):
        return self.obv() / (self.volume + 1e-6)

    def claude062(self):
        obv_series = self.obv()
        return obv_series.diff(1) / (obv_series + 1e-6)

    def claude063(self):
        return self.rolling_rank(self.obv(), window=250)

    def claude064(self):
        obv_series = self.obv()
        return (obv_series - self.sma(obv_series, 5)) / (self.sma(obv_series, 5) + 1e-6)

    def claude065(self):
        obv_series = self.obv()
        return (obv_series - self.sma(obv_series, 10)) / (self.sma(obv_series, 10) + 1e-6)

    def claude066(self):
        obv_series = self.obv()
        return obv_series.rolling(5, min_periods=5).std() / (self.sma(obv_series, 5) + 1e-6)

    def claude067(self):
        obv_series = self.obv()
        return obv_series.diff(3) / (obv_series + 1e-6)

    def claude068(self):
        obv_series = self.obv()
        return obv_series.diff(5) / (obv_series + 1e-6)

    def claude069(self):
        obv_series = self.obv()
        return obv_series.rolling(10, min_periods=10).sum() / (self.sma(self.volume, 10) + 1e-6)

    def claude070(self):
        obv_series = self.obv()
        return obv_series.rolling(20, min_periods=20).sum() / (self.sma(self.volume, 20) + 1e-6)

    # Group 84: Combined Volume-Price Factors
    def claude071(self):
        return ((self.close / (self.close.shift(1) + 1e-6) - 1) * self.volume) / (self.atr(10) + 1e-6)

    def claude072(self):
        return ((self.close / (self.close.shift(3) + 1e-6) - 1) * self.volume) / (self.atr(10) + 1e-6)

    def claude073(self):
        return ((self.close / (self.close.shift(5) + 1e-6) - 1) * self.volume) / (self.atr(10) + 1e-6)

    def claude074(self):
        return (self.momentum(self.close, 5) * self.volume) / (self.stddev(self.close, 5) + 1e-6)

    def claude075(self):
        return (self.momentum(self.close, 10) * self.volume) / (self.stddev(self.close, 10) + 1e-6)

    def claude076(self):
        return (self.momentum(self.close, 5) * self.volume) / (self.sma(self.volume, 5) + 1e-6)

    def claude077(self):
        return (self.momentum(self.close, 10) * self.volume) / (self.sma(self.volume, 10) + 1e-6)

    def claude078(self):
        return (self.close - self.open) * self.volume / (self.atr(10) + 1e-6)

    # Group 85: Aroon Indicator Variants
    def claude079(self):
        return self.close.rolling(5, min_periods=5).apply(lambda x: float(np.argmax(x)), raw=True) / 5

    def claude080(self):
        return self.close.rolling(10, min_periods=10).apply(lambda x: float(np.argmax(x)), raw=True) / 10

    def claude081(self):
        return self.close.rolling(5, min_periods=5).apply(lambda x: float(np.argmin(x)), raw=True) / 5

    def claude082(self):
        return self.close.rolling(10, min_periods=10).apply(lambda x: float(np.argmin(x)), raw=True) / 10

    def claude083(self):
        return self.rolling_rank(
            self.close.rolling(5, min_periods=5).apply(lambda x: float(np.argmax(x)) - float(np.argmin(x)), raw=True),
            window=250)

    def claude084(self):
        return self.close.rolling(10, min_periods=10).apply(lambda x: float(np.argmax(x)) - float(np.argmin(x)),
                                                            raw=True) / 10

    def claude085(self):
        return self.close.rolling(14, min_periods=14).apply(lambda x: float(np.argmax(x)) - float(np.argmin(x)),
                                                            raw=True) / 14 * 100

    # ---------------------------
    # 以下为从 alpha_120 中挑选出的不重叠且有价值的因子，重新命名为 claude086～claude105
    def claude086(self):
        """价格动量与成交量确认"""
        return self.Rank(self.Delta(self.close, 5)) * (self.volume / self.Mean(self.volume, 20))

    def claude087(self):
        """均值回归强度"""
        return -1 * self.Correlation(self.high, self.volume, 5)

    def claude088(self):
        """波动率调整后动量"""
        return self.returns / (self.stddev(self.returns, 20) + 1e-6)

    def claude089(self):
        """成交量价格趋势"""
        return self.Correlation(self.close, self.volume, 10) * self.Rank(self.Delta(self.close, 3))

    def claude090(self):
        """价格区间突破"""
        return (self.high - self.Maximum(self.high, 20)) / (self.Maximum(self.high, 20) + 1e-6)

    def claude091(self):
        """日内交易活跃度"""
        return (self.high - self.low) / ((self.amount / self.volume) + 1e-6)

    def claude092(self):
        """成交量支持下的价格趋势"""
        ma20 = self.Mean(self.close, 20)
        vol_ratio = self.volume / self.Mean(self.volume, 20)
        return self.Rank(self.close - ma20 * vol_ratio)

    def claude093(self):
        """波动率状态检测"""
        high_vol = self.stddev(self.returns, 5)
        low_vol = self.stddev(self.returns, 20)
        return self.Rank(high_vol - low_vol)

    def claude094(self):
        """相对市场的价格动量"""
        stock_ret = self.Delta(self.close, 5) / (self.close + 1e-6)
        mkt_ret = self.Delta(self.benchmark_close, 5) / (
                    self.benchmark_close + 1e-6) if self.benchmark_close is not None else 0
        return stock_ret - mkt_ret

    def claude095(self):
        """成交量加权价格动量"""
        return self.Correlation(self.vwap, self.Decay_linear(self.volume, 10), 10)

    def claude096(self):
        """均值回归指标（基于布林带）"""
        bb = self.calculate_BB(period=20)
        return -1 * self.Rank(bb)

    def claude097(self):
        """价格加速度"""
        mom1 = self.Delta(self.close, 1) / (self.close + 1e-6)
        mom5 = self.Delta(self.close, 5) / (self.close + 1e-6)
        return self.Rank(mom1 - mom5)

    def claude098(self):
        """成交量价格背离"""
        # 注意：此处使用了外部函数 correlation_with_linear_range（需自行定义或确保可用）
        # 这里假设该函数已在全局定义
        price_trend = correlation_with_linear_range(self.close, 10)
        vol_trend = correlation_with_linear_range(self.volume, 10)
        return -1 * (price_trend - vol_trend)

    def claude099(self):
        """日内波动率趋势"""
        return self.stddev(self.high / (self.low + 1e-6), 10)

    def claude100(self):
        """RSI 动量"""
        rsi = self.calculate_RSI(14)
        return self.Delta(rsi, 3)

    def claude101(self):
        """MACD 趋势强度"""
        macd = self.calculate_MACD()
        return self.Rank(np.abs(macd))

    def claude102(self):
        """成交量调整后收益率"""
        ret = self.returns * (self.volume / self.Mean(self.volume, 20))
        return self.Decay_linear(ret, 5)

    def claude103(self):
        """价格缺口分析"""
        overnight_gap = self.open / (self.close.shift(1) + 1e-6) - 1
        return self.Rank(overnight_gap)

    def claude104(self):
        """成交量加权趋势"""
        vwap_trend = self.Delta(self.vwap, 5) / (self.vwap + 1e-6)
        vol_weight = self.volume / self.Mean(self.volume, 20)
        return vwap_trend * vol_weight

    def claude105(self):
        """相对市场强度"""
        stock_strength = self.close / (self.Mean(self.close, 20) + 1e-6)
        market_strength = self.benchmark_close / (
                    self.Mean(self.benchmark_close, 20) + 1e-6) if self.benchmark_close is not None else 0
        return stock_strength - market_strength
