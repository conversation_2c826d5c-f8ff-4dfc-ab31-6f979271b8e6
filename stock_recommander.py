from PIL import Image, ImageChops
import os.path

import akshare
import fire
from alphalens.tears import create_returns_tear_sheet

import util
from news_anylizer import NewsAnalyzer
from news_scrawler import generate_for_today
from util import *


def get_fund_flow(x, stock_zh_a_spot_df):
    codes = stock_zh_a_spot_df['代码'].apply(lambda x: x[2:])
    markets = stock_zh_a_spot_df['代码'].apply(lambda x: x[0:2])
    market = markets[codes.loc[codes == x.name].index].values[0]
    stock_individual_fund_flow_df = akshare.stock_individual_fund_flow(stock=x.name, market=market)
    flow5 = stock_individual_fund_flow_df.iloc[-6:-1]['主力净流入-净额'].sum()
    flow5 = '{:,}'.format(flow5)
    flow10 = stock_individual_fund_flow_df.iloc[-11:-1]['主力净流入-净额'].sum()
    flow10 = '{:,}'.format(flow10)
    flow1 = stock_individual_fund_flow_df.iloc[-1]['主力净流入-净额']
    flow1 = '{:,}'.format(flow1)
    return pd.Series({'flow1': flow1, 'flow5': flow5, 'flow10': flow10}, name=x.name)


def get_news_info(x):
    info = akshare.stock_individual_info_em(x.name)
    info.set_index('item', inplace=True)
    info = info.T
    info.drop(columns=['流通市值', '上市时间', '总股本', '流通股'], inplace=True)
    info.set_index('股票代码', inplace=True)
    info['总市值'] = info['总市值'] / 1e9
    today = pd.to_datetime('today')
    start_time = today - pd.Timedelta(days=30)
    start_time = start_time.strftime('%Y-%m-%d')
    scores = NewsAnalyzer.get_instance().get_news_score(x.name, start_time)
    info = pd.concat([info, scores], axis=1)
    return info.iloc[0]


def get_price_lines(data, end_date, start_date):
    data.index = pd.to_datetime(data.index)
    init_val = data.loc[data.index == start_date, 'close']
    end_date = min(pd.to_datetime(end_date), data.index.max())
    end_val = data.loc[data.index >= end_date, 'close']
    draw_down = data.loc[(data.index > start_date) & (data.index <= end_date), 'close'].min()
    draw_down = (init_val.iloc[0] - draw_down) / init_val.iloc[0]
    returns = (end_val.iloc[0] - init_val.iloc[0]) / init_val.iloc[0]
    if returns < -0.1:
        returns = -0.1
    return pd.Series(
        {'start_price': init_val.iloc[0], 'end_price': end_val.iloc[0], 'real_return': returns, 'draw_down': draw_down})


def get_price_info_batch(stock, end_date):
    stock_name = stock.name[1]
    start_date = stock.name[0]
    end_date = min((pd.to_datetime(start_date) + pd.Timedelta(days=14)).strftime('%Y%m%d'), end_date)
    data = pd.read_csv(f'{config_info["market"]}_daily/{stock_name}.csv', index_col=0)
    return get_price_lines(data, end_date, start_date)


def evaluate_return_infos(start, end, model_name=None):
    factor_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])
    df = load_stock_data('a101', config_info['market'])

    prepared_data = prepare_data_for_model(df, factor_names, config_info['period_n'], start,
                                           end)
    predictor = load_model(config_info['model_name'] if model_name is None else model_name)
    predicted_data = predict_proba(predictor, prepared_data)
    predicted_data = prepare_bt_pred_data(predicted_data, df, 0)
    stock_codes: pd.DataFrame = predicted_data.loc[
        (predicted_data['date'] >= start) & (predicted_data['date'] < end) & (predicted_data['rank'] <= 5), ['date',
                                                                                                             'sec_id',
                                                                                                             'return',
                                                                                                             'rank']]
    stock_codes.set_index(['date', 'sec_id'], inplace=True)
    price_info = stock_codes.apply(lambda x: get_price_info_batch(x, end), axis=1)
    stock_codes = pd.concat([stock_codes, price_info], axis=1)
    return stock_codes


def get_ret_now(x, prepared_data):
    if pd.isna(x.ret10):
        max_date = prepared_data.loc[prepared_data.index.get_level_values(1) == x.name[1]].index.get_level_values(
            0).max()
        ret = prepared_data.loc[(prepared_data.index.get_level_values(0) == max_date) & (
                prepared_data.index.get_level_values(1) == x.name[1]), 'close'] - x.close
        ret = ret[0] / x.close
    else:
        ret = x.ret10

    return pd.Series({'ret_now': ret}, name=x.name)


def remove_white_border(img):
    """
    去除图片的白边。方法是计算图片与全白图片的差异，
    得到非白色区域的边界，然后对图片进行裁剪。
    """
    if img.mode in ("RGB", "RGBA"):
        white_bg = Image.new(img.mode, img.size, (255, 255, 255))
    else:
        white_bg = Image.new(img.mode, img.size, 255)

    diff = ImageChops.difference(img, white_bg)
    bbox = diff.getbbox()
    if bbox:
        return img.crop(bbox)
    else:
        return img


def remove_white_border_in_folder(folder_path):
    """
    遍历指定文件夹中的所有文件，对扩展名为 .png 的图片调用 remove_white_border 函数，
    裁剪掉白边后保存图片（覆盖原图）。
    """
    for file_name in os.listdir(folder_path):
        if file_name.lower().endswith('.png'):
            file_path = os.path.join(folder_path, file_name)
            try:
                with Image.open(file_path) as img:
                    cropped_img = remove_white_border(img)
                    cropped_img.save(file_path)
            except Exception as e:
                print(f"处理 {file_path} 时出错: {e}")


def research_alpha(start, end, alpha_name, model_name=None):
    if not os.path.exists('alphas_fig'):
        os.mkdir('alphas_fig')
    df = load_stock_data(config_info['alpha_type'], config_info['market'])

    if model_name is not None:
        factor_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])
        predicted_data, _ = get_predicted_data(df, end, factor_names, model_name, start)
        predicted_data = predicted_data[predicted_data['rank'] < config_info['topn']]
        predicted_data.set_index(['date', 'sec_id'], inplace=True)
        df = pd.merge(df, predicted_data, left_index=True, right_index=True, how='inner')

    df = df.loc[
        (df.index.get_level_values(0) >= start) & (df.index.get_level_values(0) <= end)
        ]
    close = df.pivot_table(index='date', columns='sec_id', values='close')
    if alpha_name is not None:
        analyze_and_plot_factor(df[alpha_name], close, alpha_name)
        return
    for col in df.columns:
        if col in ['adjclose', 'return', 'open', 'high', 'low', 'close', 'volume', 'vwap', 'weight']:
            continue
        try:
            analyze_and_plot_factor(df[col], close, col)
        except Exception as e:
            print(f'{col} error: {e}')


def analyze_and_plot_factor(factor_data, close_data, col_name, periods=(1, 5, 10), quantiles=5, max_loss=0.35):
    print(f'-------{col_name}-------------')
    factors = alphalens.utils.get_clean_factor_and_forward_returns(
        factor_data,
        close_data,
        bins=None,
        periods=periods,
        quantiles=quantiles,
        max_loss=max_loss
    )

    if factors.empty:
        print(f"Factors for {col_name} are empty. Skipping...")
        return

    print(factors.head())
    ic = alphalens.performance.mean_information_coefficient(factors)
    print(ic)
    print("Creating returns tear sheet...")

    def save_all_figs():
        fig = plt.figure(2)
        if not os.path.exists('alphas_fig'):
            os.mkdir('alphas_fig')
        fig.savefig(f'alphas_fig/{col_name}.png')

    original_show = plt.show
    original_close = plt.close
    plt.show = save_all_figs
    plt.close = lambda *args, **kwargs: None

    create_returns_tear_sheet(factors, long_short=False)

    plt.show = original_show
    plt.close = original_close

    print("Tear sheet created.")

    plt.close('all')

    with Image.open(f'alphas_fig/{col_name}.png') as img:
        cropped_img = remove_white_border(img)
        cropped_img.save(f'alphas_fig/{col_name}.png')


def get_predicted_data(df, end, factor_names, model_name, start):
    prepared_data = prepare_data_for_model(df, factor_names, config_info['period_n'], start, end)
    predictor = load_model(model_name)
    if config_info['sub_model_name'] is not None:
        sub_model_name = config_info['sub_model_name']
    elif config_info['enable_second_ensemble'] and config_info['num_stack_levels'] == 0:
        sub_model_name = 'WeightedEnsemble_L2Best'
        all_models = predictor.model_names()
        if sub_model_name not in all_models:
            l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]
            predictor.fit_weighted_ensemble(
                base_models=list(l1_models))
    else:
        sub_model_name = predictor.model_best
    predicted_data = predict_proba(predictor, prepared_data, sub_model_name)
    predicted_data = prepare_bt_pred_data(predicted_data, df, 0)
    predicted_data.rename(columns={'return': "predict"}, inplace=True)
    return predicted_data, prepared_data['return']


def get_relative_returns(start, end, model_name, topn, prefix='', selected_alpha=None, tuning_sec_only=False):
    if selected_alpha is not None and selected_alpha.startswith('_'):
        selected_alpha = selected_alpha[1:]
        ascending = True
    else:
        ascending = False
    factor_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'], 1000)
    df = load_stock_data(config_info['alpha_type'], config_info['market'], prefix)
    predicted_data, returns = get_predicted_data(df, end, factor_names, model_name, start)
    predicted_data['date'] = pd.to_datetime(predicted_data['date'])
    if tuning_sec_only:
        with open('tuning_sec_ids.txt', 'r') as f:
            tuning_sec = f.read().splitlines()
            predicted_data = predicted_data[predicted_data['sec_id'].isin(tuning_sec)]
    predicted_data.set_index(['date', 'sec_id'], inplace=True)
    predicted_data = pd.merge(predicted_data, returns, left_index=True, right_index=True)
    predicted_data = pd.merge(predicted_data, df['close'], left_index=True, right_index=True)
    predicted_data = pd.merge(predicted_data, df['open'], left_index=True, right_index=True)
    if selected_alpha is not None:
        predicted_data = pd.merge(predicted_data, df[selected_alpha], left_index=True, right_index=True)
    close = predicted_data.pivot_table(index='date', columns='sec_id', values='close')
    analyze_and_plot_factor(predicted_data['predict'], close, 'predict', periods=(1, 3, 5, 10), quantiles=10,
                            max_loss=0.35)

    if os.path.exists(f'alphas_fig/predict.png'):
        os.rename(f'alphas_fig/predict.png', f'alphas_fig/{model_name}.png')

    predicted_data['date'] = predicted_data.index.get_level_values(0)
    predicted_data['sec_id'] = predicted_data.index.get_level_values(1)

    predicted_data.reset_index(drop=True, inplace=True)
    predicted_data = predicted_data.sort_values(by=['sec_id', 'date'])

    holding_days = int(config_info['topn'] / config_info['max_sell'])
    predicted_data['return_1D'] = predicted_data.groupby('sec_id')['open'].bfill().pct_change(holding_days)
    predicted_data['return_1D'] = predicted_data.groupby('sec_id')['return_1D'].shift(-1 - holding_days)
    predicted_data = predicted_data.dropna(subset=['return_1D'])
    if selected_alpha is None:
        predicted_data = predicted_data[predicted_data['rank'] <= topn]
        ret = predicted_data.groupby('date').apply(lambda x: (1 + x.loc[:, 'return_1D']).mean())
    else:
        predicted_data = predicted_data[predicted_data['rank'] <= topn * 2]
        predicted_data['selected_alpha_rank'] = predicted_data.groupby('date')[selected_alpha].rank(ascending=ascending)
        predicted_data = predicted_data[predicted_data['selected_alpha_rank'] <= topn]
        ret = predicted_data.groupby('date').apply(lambda x: (1 + x.loc[:, 'return_1D']).mean())

    return ret


def get_return_infos_of_range(start, end, model_name, prefix='', topn=5, assist_model_name=None):
    if model_name is None:
        model_name = config_info['model_name']
    print(f'use model {model_name}')
    factor_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])
    df = load_stock_data(config_info['alpha_type'], config_info['market'], prefix)
    end_delayed = (pd.to_datetime(end) + pd.Timedelta(days=30)).strftime('%Y%m%d')
    prepared_data = prepare_data_for_model(df, factor_names, config_info['period_n'], start, end_delayed,
                                           keep_close=True)
    predictor = load_model(model_name)
    predicted_data = predict_proba(predictor, prepared_data)
    predicted_data = prepare_bt_pred_data(predicted_data, df, 0)
    predicted_data.rename(columns={'return': "predict"}, inplace=True)
    predicted_data['date'] = pd.to_datetime(predicted_data['date'])
    predicted_data.set_index(['date', 'sec_id'], inplace=True)
    prepared_data['ret1'] = prepared_data.groupby('sec_id').close.pct_change(1)
    prepared_data['ret5'] = prepared_data.groupby('sec_id').close.pct_change(5)
    prepared_data['ret10'] = prepared_data.groupby('sec_id').close.pct_change(10)

    prepared_data['ret1'] = prepared_data['ret1'].shift(-1)
    prepared_data['ret5'] = prepared_data['ret5'].shift(-5)
    prepared_data['ret10'] = prepared_data['ret10'].shift(-10)

    ret_now = prepared_data.apply(lambda x: get_ret_now(x, prepared_data), axis=1)
    prepared_data = pd.concat([prepared_data, ret_now], axis=1)

    ret = pd.merge(prepared_data.loc[:, ['ret1', 'ret5', 'ret10', 'ret_now']], predicted_data,
                   left_index=True, right_index=True)

    if assist_model_name is not None:
        print(f'use model {assist_model_name}')
        assist_factors = get_feature_names(config_info['alpha_type'], config_info['market'],
                                           config_info['assist_period_n'], config_info['assist_feature_count'])
        assist_data = prepare_data_for_model(df, assist_factors, config_info['assist_period_n'], start, end_delayed)
        predictor = load_model(assist_model_name)
        assist_predicted_data = predict_proba(predictor, assist_data)
        assist_predicted_data = prepare_bt_pred_data(assist_predicted_data, df, 0)
        assist_predicted_data = assist_predicted_data.loc[:, ['date', 'sec_id', 'return']]
        assist_predicted_data.set_index(['date', 'sec_id'], inplace=True)
        assist_predicted_data.rename(columns={'return': 'assist_predict'}, inplace=True)
        ret = pd.merge(ret, assist_predicted_data, left_index=True, right_index=True, how='inner')
        ret.rename(columns={'predict': 'main_predict'}, inplace=True)
        ret['predict'] = (ret['main_predict'] * 0.6 + ret['assist_predict'] * 0.4)
        ret['rank'] = ret.groupby('date')['predict'].rank(ascending=False)
        ret.sort_values(['date', 'rank'], inplace=True)

    return ret[(ret.index.get_level_values(0) <= end) & (ret['rank'] <= topn)]


def get_return_infos(day, stocks=[], topn=5, prefix=''):
    print(f'use model {config_info["model_name"]}')
    print(f'use assist model {config_info["assist_model_name"]}')
    if isinstance(day, str):
        day = pd.to_datetime(day)
    prepared_start_date = day - pd.Timedelta(days=400)
    factor_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])
    df = load_stock_data(config_info['alpha_type'], config_info['market'], prefix)

    prepared_data = prepare_data_for_model(df, factor_names, config_info['period_n'], prepared_start_date,
                                           day)
    predictor = load_model(config_info['model_name'])
    predicted_data = predict_proba(predictor, prepared_data)
    predicted_data = prepare_bt_pred_data(predicted_data, df, 0)
    predicted_data['rank_prev1'] = predicted_data.groupby('sec_id')['rank'].shift(1)
    predicted_data['rank_prev2'] = predicted_data.groupby('sec_id')['rank'].shift(2)
    predicted_data['ret_prev1'] = predicted_data.groupby('sec_id')['return'].shift(1)
    predicted_data['ret_prev2'] = predicted_data.groupby('sec_id')['return'].shift(2)

    str_day = prepared_data.index.get_level_values(0).max()
    mean_return = predicted_data.loc[(predicted_data['date'] == str_day), 'return'].mean()
    stock_codes: pd.DataFrame = predicted_data.loc[
        (predicted_data['date'] == str_day), ['sec_id', 'return', 'rank',
                                              'rank_prev1', 'rank_prev2',
                                              'ret_prev1',
                                              'ret_prev2', 'vwap5']]

    if config_info['assist_model_name'] != config_info['model_name']:
        assist_factors = get_feature_names(config_info['alpha_type'], config_info['market'],
                                           config_info['assist_period_n'], config_info['assist_feature_count'])
        assist_data = prepare_data_for_model(df, assist_factors, config_info['assist_period_n'], prepared_start_date,
                                             day)
        predictor = load_model(config_info['assist_model_name'])
        assist_predicted_data = predict_proba(predictor, assist_data)
        assist_predicted_data = prepare_bt_pred_data(assist_predicted_data, df, 0)
        assist_predicted_data = assist_predicted_data.loc[
            assist_predicted_data['date'] == str_day, ['sec_id', 'return']]

        assist_predicted_data.rename(columns={'return': 'assist_return'}, inplace=True)
        stock_codes = stock_codes.merge(assist_predicted_data, on=['sec_id'], how='left')
        stock_codes.rename(columns={'return': 'main_return'}, inplace=True)
        stock_codes['return'] = (stock_codes['main_return'] * 0.6 + stock_codes['assist_return'] * 0.4)
        stock_codes['rank'] = stock_codes['return'].rank(ascending=False)
        stock_codes.sort_values(['rank'], inplace=True)

    stock_codes['sec_id'] = stock_codes['sec_id'].apply(lambda x: str(int(x)).zfill(6))
    ret = stock_codes.iloc[0:topn, :] if topn > 0 else stock_codes
    ret = pd.concat([ret, stock_codes.loc[stock_codes['sec_id'].isin(stocks), :]], axis=0).drop_duplicates()
    ret['holding'] = ret['sec_id'].isin(stocks)
    ret.insert(2, 'holding', ret.pop('holding'))

    ret.set_index('sec_id', inplace=True)
    ret.sort_values(by='return', ascending=False, inplace=True)
    return ret, mean_return


def recommend_for_today(prefix='', today=None, with_gpt=True, with_news=False, topn=-1):
    if today is None:
        today = get_last_time()
    today = pd.to_datetime(today)
    stock_codes, mean_return = get_return_infos(today, config_info['interest'], topn * 2, prefix)
    print(stock_codes)
    last_day = today.strftime('%Y-%m-%d')
    stock_codes.to_csv(f'./{last_day}_infos.csv')

    title = f'gpt{with_gpt}_{last_day}平均收益率为{mean_return:.3f}, {config_info["market"]}'
    logging.getLogger().info(title)
    try:
        stock_zh_a_spot_df = akshare.stock_zh_a_spot()

        if with_news:
            news_info = stock_codes.apply(lambda x: get_news_info(x), axis=1)
            stock_codes = pd.concat([stock_codes, news_info], axis=1)

        fund_flow = stock_codes.apply(lambda x: get_fund_flow(x, stock_zh_a_spot_df), axis=1)
        stock_codes = pd.concat([stock_codes, fund_flow], axis=1)
    except Exception as e:
        logging.error(f'get fund flow error: {e}')
    try:
        comments = akshare.stock_comment_em()
        comments.rename(columns={'代码': 'sec_id'}, inplace=True)
        comments.set_index('sec_id', inplace=True)
        comments.to_csv(os.path.expanduser('~/stock_comments.csv'), index=True)
        stock_codes = stock_codes.join(comments, how='left')
    except Exception as e:
        logging.error(f'get comments error: {e}')

    wencai_info = pd.read_csv(f'wencai-{today.strftime("%Y%m%d")}.csv', index_col=0, dtype={0: str})
    selected_columns = ['牛叉诊股综合评分', '技术面评分', '资金面评分', '消息面评分', '行业面评分', '基本面评分',
                        '拟合评分']
    stock_codes = stock_codes.join(wencai_info[selected_columns], how='left')
    stock_codes = stock_codes[~stock_codes['名称'].str.contains('ST')]
    stock_codes = stock_codes.sort_values(by='rank')
    dump_file = f'today_{config_info["market"]}.csv'
    stock_codes.to_csv(dump_file)

    if os.path.exists('./output'):
        for file in os.listdir('./output'):
            os.remove(f'./output/{file}')
    plot_stock_charts(stock_codes.index.tolist(), './output')
    files = [f'./output/{f}.png' for f in stock_codes.index.tolist() if os.path.exists(f'./output/{f}.png')]

    files.append(dump_file)
    notify_by_email_with_attachment(title, *files)
    logging.getLogger().info(stock_codes)
    if with_gpt:
        final_summary, summary = generate_for_today()
        notify_by_email('gpt summary', '\n'.join([summary, final_summary]), also_to_jammie=True)


def get_price_info(stock, start_date, end_date):
    data = pd.read_csv(f'{config_info["market"]}_daily/{stock.name}.csv', index_col=0)
    return get_price_lines(data, start_date, end_date)


if __name__ == '__main__':
    util.init_logger()
    util.init_qlib()
    fire.Fire(recommend_for_today)
