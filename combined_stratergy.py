from util import *


class CombinedTopNStratergy(TopNStratergy):
    params = (
        ('pred_data_assist', None),
    )

    def __init__(self):
        super().__init__()
        self.pred_data_assist = self.p.pred_data_assist
        self._pred_data_combined = self.pred_data.copy()
        self._pred_data_combined['rank'] = self._pred_data_combined.groupby('date')['return'].rank(ascending=False)

        self._pred_data_combined['assist_return'] = self.pred_data_assist['return']
        self._pred_data_combined = self._pred_data_combined[self._pred_data_combined['rank'] <= self.topn * 1.5]

        self._pred_data_combined['assist_rank'] = self._pred_data_combined.groupby('date')['assist_return'].rank(
            ascending=False)

    def get_selected_sec(self, dt, i, predict_date):
        selected = self._pred_data_combined.loc[
            (self._pred_data_combined['date'] == predict_date) & (self._pred_data_combined['assist_rank'] == i + 1), [
                'sec_id',
                'return']]
        if selected.empty:
            return None, None, True
        else:
            sec_code = selected.iloc[0]['sec_id']
            returns = selected.iloc[0]['return']
            return sec_code, returns, False


def test_combine_stratergy(alpha_type, test_start_date, end_date, main_model_name, assist_model_name, main_period,
                           assist_period):
    logging.getLogger().info('test_combine_stratergy: from %s to %s,  combine %s and %s', test_start_date,
                             end_date,
                             main_model_name, assist_model_name)

    df = load_stock_data(alpha_type, config_info['market'])
    factor_names = get_feature_names(alpha_type, config_info['market'], main_period)
    predictor = load_model(main_model_name)
    test_data = prepare_data_for_model(df, factor_names, main_period, test_start_date,
                                       end_date)
    predicted_data = predict_proba(predictor, test_data)
    backtrader_pred = prepare_bt_pred_data(predicted_data, df, 0)

    factor_names = get_feature_names(alpha_type, config_info['market'], assist_period)
    test_data_asm = prepare_data_for_model(df, factor_names, assist_period, test_start_date, end_date)
    predictor = load_model(assist_model_name)
    predicted_data_asm = predict_proba(predictor, test_data_asm)
    backtrader_pred_asm = prepare_bt_pred_data(predicted_data_asm, df, 0)

    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date)
    trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]
    cerebro.addstrategy(CombinedTopNStratergy,
                        pred_data=backtrader_pred,
                        pred_data_assist=backtrader_pred_asm,
                        topn=config_info['topn'],
                        period=main_period,
                        max_down=0.1,
                        min_hold=1000,
                        printlog=False,
                        trade_data=trade_data)
    prepare_cerebro(cerebro)
    stats = cerebro.run(optreturn=True)
    loginfo = 'test combined stratergy with model: ' + main_model_name + ' from ' + test_start_date + ' to ' + end_date
    dump_stats_cerebro(cerebro, stats, loginfo)
