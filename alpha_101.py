from numpy import abs
from numpy import log
from numpy import sign
from scipy.stats import percentileofscore

from alphabase import *


def ts_var(df, window=10):
    return df.rolling(window, min_periods=min_periods_window(window)).var()


# 新增：均绝对离差（MAD）函数，对应公式中的 Mad
def mad(df, window=10):
    return df.rolling(window, min_periods=min_periods_window(window)).apply(lambda x: np.mean(np.abs(x - x.mean())))


def rsi(df, window=14):
    """
    计算相对强弱指标(RSI)
    RSI = 100 - 100 / (1 + RS)
    RS 为窗口内平均上涨幅度与平均下跌幅度之比。
    """
    delta = df.diff()
    up = delta.clip(lower=0)
    down = -delta.clip(upper=0)
    roll_up = up.rolling(window, min_periods=min_periods_window(window)).mean()
    roll_down = down.rolling(window, min_periods=min_periods_window(window)).mean()
    rs = roll_up / (roll_down + 1e-8)
    return 100 - 100 / (1 + rs)


def obv(close, volume):
    """
    计算平衡成交量(OBV)
    OBV为累计的成交量，若当日收盘价上升则加上成交量，下降则减去成交量。
    """
    diff = close.diff()
    sign = diff.apply(np.sign)
    return (sign * volume).cumsum()


def atr(high, low, close, window=14):
    """
    计算平均真实波幅(ATR)
    ATR考虑了价格区间和跳空缺口，反映股票波动率。
    """
    tr = np.maximum(high - low,
                    np.maximum(np.abs(high - delay(close, 1)),
                               np.abs(low - delay(close, 1))))
    return tr.rolling(window, min_periods=min_periods_window(window)).mean()


def returns(df):
    return df.rolling(2).apply(lambda x: x.iloc[-1] / x.iloc[0]) - 1


def ts_sum(df, window=10):
    """
    Wrapper function to estimate rolling sum.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series min over the past 'window' days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).sum()


def sma(df, window=10):
    """
    Wrapper function to estimate SMA.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series min over the past 'window' days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).mean()


def stddev(df, window=10):
    """
    Wrapper function to estimate rolling standard deviation.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series min over the past 'window' days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).std()


def correlation(x, y, window=10):
    return x.fillna(0).rolling(window).corr(y.fillna(0)).fillna(0).replace([np.inf, -np.inf], 0)


def covariance(x, y, window=10):
    return x.fillna(0).rolling(window).cov(y.fillna(0))


def rolling_rank(na):
    """
    Auxiliary function to be used in pd.rolling_apply
    :param na: numpy array.
    :return: The rank of the last value in the array.
    """
    return percentileofscore(na, na[-1], 'rank') / 100


def ts_rank(df, window=10):
    """
    Wrapper function to estimate rolling rank.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series rank over the past window days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).apply(rolling_rank)


def rolling_prod(na):
    """
    Auxiliary function to be used in pd.rolling_apply
    :param na: numpy array.
    :return: The product of the values in the array.
    """
    return np.prod(na)


def product(df, window=10):
    """
    Wrapper function to estimate rolling product.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series product over the past 'window' days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).apply(rolling_prod)


def ts_min(df, window=10):
    """
    Wrapper function to estimate rolling min.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series min over the past 'window' days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).min()


def ts_max(df, window=10):
    """
    Wrapper function to estimate rolling min.
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: a pandas DataFrame with the time-series max over the past 'window' days.
    """
    return df.rolling(window, min_periods=min_periods_window(window)).max()


def rolling_dot(df, v, windows=10):
    return df.rolling(windows).apply(lambda x: np.dot(x, v))


def delta(df, period=1):
    """
    Wrapper function to estimate difference.
    :param df: a pandas DataFrame.
    :param period: the difference grade.
    :return: a pandas DataFrame with today’s value minus the value 'period' days ago.
    """
    return df.diff(period)


def delta_rate(df, period=1):
    return df.diff(period) / df.shift(period)


def delay(df, period=1):
    """
    Wrapper function to estimate lag.
    :param df: a pandas DataFrame.
    :param period: the lag grade.
    :return: a pandas DataFrame with lagged time series
    """
    return df.shift(period)


def rank(df):
    return df.rank(axis=1, method='min', pct=True)


def scale(df, k=1, win_size=250):
    def compute_ratio(window):
        abs_sum = np.sum(np.abs(window))
        return 0 if abs_sum == 0 else window.iloc[-1] / abs_sum

    ret = df.rolling(window=win_size, min_periods=min_periods_window(win_size)).apply(compute_ratio)
    return ret.mul(k)


def ts_argmax(df, window=10):
    """
    Wrapper function to estimate which day ts_max(df, window) occurred on
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: well.. that :)
    """
    return df.rolling(window, min_periods=min_periods_window(window)).apply(np.argmax) + 1


def ts_argmin(df, window=10):
    """
    Wrapper function to estimate which day ts_min(df, window) occurred on
    :param df: a pandas DataFrame.
    :param window: the rolling window.
    :return: well.. that :)
    """
    return df.rolling(window, min_periods=min_periods_window(window)).apply(np.argmin) + 1


def decay_linear(df, period=10):
    weights = np.array(range(1, period + 1))
    sum_weights = np.sum(weights)
    return df.rolling(period).apply(lambda x: np.sum(weights * x.fillna(0)) / sum_weights)


def max(sr1, sr2):
    return np.maximum(sr1, sr2)


def min(sr1, sr2):
    return np.minimum(sr1, sr2)


class Alphas101(Alphas):
    def __init__(self, df_data):
        super().__init__(df_data)
        self.open = df_data['open']
        self.high = df_data['high']
        self.low = df_data['low']
        self.close = df_data['close']
        self.volume = df_data['volume']
        self.returns = df_data['change']
        self.vwap = df_data['vwap']

    def alpha001(self):
        inner = (-stddev(self.returns, 10)) * (self.close - delay(self.close, 10)) / delay(self.close, 10)
        return rank(inner)

    def alpha002(self):
        df = -correlation(delta_rate(self.volume, 1),
                          self.returns, 10)
        return df

    def alpha003(self):
        df = -1 * correlation(delta_rate(self.open, 1), delta_rate(self.volume), 10)
        return df

    def alpha004(self):
        return -1 * ts_rank(self.low, 15)

    def alpha005(self):
        return rank((self.open - (ts_sum(self.vwap, 10) / 10)) / self.open) * (
                -1 * abs(rank((self.close - self.vwap)) / self.close))

    def alpha006(self):
        df = -1 * correlation(self.open, self.volume, 20)
        return df

    def alpha007(self):
        adv20 = sma(self.volume, 20)
        alpha = ts_rank(delta(self.close, 5), 60) ** ((self.volume - adv20) / adv20)
        return alpha

    def alpha008(self):
        inner = ts_sum(self.open, 5) * ts_sum(self.returns, 5)
        return rank((inner - delay(inner, 10)) / delay(inner, 10))

    def alpha009(self):
        return rolling_dot(self.returns, [0.8, 0.9, 1, 1.1, 1.2], 5)

    def alpha010(self):
        return rolling_dot(sign(self.returns), np.linspace(0.5, 1.5, 10), 10)

    def alpha011(self):
        return ((rank(ts_max((self.vwap - self.close) / self.close, 3))) * rank(
            delta_rate(self.volume, 3)))

    def alpha012(self):
        return sign(delta(self.volume, 1)) * (-1 * delta_rate(self.close, 1))

    def alpha013(self):
        return -1 * rank(covariance(rank(delta_rate(self.close)), rank(delta_rate(self.volume)), 5))

    def alpha014(self):
        df = correlation(self.open, self.volume, 10)
        return -1 * rank(delta(self.returns, 3)) * df

    def alpha015(self):
        df = correlation(rank(self.high), rank(self.volume), 5)
        return -1 * ts_sum(rank(df), 5)

    def alpha016(self):
        return -1 * rank(covariance(rank(self.high), rank(self.volume), 5))

    def alpha017(self):
        adv20 = sma(self.volume, 20)
        return -1 * (rank(ts_rank(self.close, 10)) *
                     rank(delta(delta(self.close, 1), 1)) *
                     rank(ts_rank((self.volume / adv20), 5)))

    def alpha018(self):
        df = correlation(self.close, self.open, 10)
        return -1 * (
            rank((stddev(abs((self.close - self.open) / self.open), 5) + (self.close - self.open) / self.open) +
                 df))

    def alpha019(self):
        return ((-1 * sign((self.close - delay(self.close, 7)) + delta_rate(self.close, 7))) *
                (1 + rank(1 + ts_sum(self.returns, 60))))

    def alpha020(self):
        return rank((self.open - delay(self.high, 1)) / self.open) * rank(
            (self.open - delay(self.close, 1)) / self.open) * rank((self.open - delay(self.low, 1)) / self.open)

    def alpha021(self):
        cond_1 = sma(self.close, 8) + stddev(self.close, 8) < sma(self.close, 2)
        cond_2 = sma(self.close, 2) < sma(self.close, 8) - stddev(self.close, 8)
        cond_3 = sma(self.volume, 20) / self.volume > 1

        return (cond_1 | ((~cond_1) & (~cond_2) & (~cond_3))).astype('int') * (-2) + 1

    def alpha022(self):
        df = correlation(self.high, self.volume, 5)
        return -1 * delta(df, 5) * rank(stddev(self.close, 20) / self.close)

    def alpha023(self):
        cond = sma(self.high, 20) < self.high
        alpha = self.close.copy(
            deep=True)
        alpha[cond] = -1 * delta(self.high, 2).fillna(value=0)
        alpha[~cond] = 0
        return alpha

    def alpha024(self):
        cond = delta(sma(self.close, 100), 100) / delay(self.close, 100) <= 0.05
        alpha = -1 * delta_rate(self.close, 3)
        alpha[cond] = -1 * (self.close - ts_min(self.close, 100)) / ts_min(self.close, 100)
        return alpha

    def alpha025(self):
        adv20 = sma(self.volume, 20)
        return rank(((((-1 * self.returns) * adv20 / self.volume) * self.vwap / self.close) * (
                self.high - self.close) / self.close))

    def alpha026(self):
        df = correlation(ts_rank(self.volume, 5), ts_rank(self.high, 5), 5)
        return -1 * ts_max(df, 3)

    def alpha027(self):
        alpha = rank((sma(correlation(rank(self.volume), rank(self.vwap), 6), 2) / 2.0))
        return sign((alpha - 0.5) * (-2))

    def alpha028(self):
        adv20 = sma(self.volume, 20)
        df = correlation(adv20, self.low, 5)
        return scale(((df + ((self.high + self.low) / 2)) - self.close))

    def alpha029(self):
        return (ts_min(rank(rank(scale(log(ts_sum(rank(rank(-1 * rank(delta_rate((self.close - 1), 5)))), 2))))), 5) +
                ts_rank(delay((-1 * self.returns), 6), 5))

    def alpha030(self):
        delta_close = delta(self.close, 1)
        inner = sign(delta_close) + sign(delay(delta_close, 1)) + sign(delay(delta_close, 2))
        return ((1.0 - rank(inner)) * ts_sum(self.volume, 5)) / ts_sum(self.volume, 20)

    def alpha031(self):
        adv20 = sma(self.volume, 20)
        df = correlation(adv20, self.low, 12)
        p1 = rank(decay_linear((-1 * rank(delta_rate(self.close, 10))), 10))
        p2 = rank((-1 * delta_rate(self.close, 3)))
        p3 = sign(scale(df))

        return p1 + p2 + p3

    def alpha032(self):
        return scale(((sma(self.close, 7) / 7) - self.close)) + (
                20 * scale(correlation(self.vwap, delay(self.close, 5), 230)))

    def alpha033(self):
        return rank(-1 + (self.open / self.close))

    def alpha034(self):
        inner = stddev(self.returns, 2) / stddev(self.returns, 5)
        inner = inner.replace([-np.inf, np.inf], 1).fillna(value=1)
        return rank(2 - rank(inner) - rank(delta_rate(self.close, 1)))

    def alpha035(self):
        return ((ts_rank(self.volume, 32) *
                 (1 - ts_rank(self.close + self.high - self.low, 16))) *
                (1 - ts_rank(self.returns, 32)))

    def alpha036(self):
        adv20 = sma(self.volume, 20)
        return (((((2.21 * rank(correlation((self.close - self.open), delay(self.volume, 1), 15))) + (
                0.7 * rank((self.open - self.close) / self.close))) + (
                          0.73 * rank(ts_rank(delay((-1 * self.returns), 6), 5)))) + rank(
            abs(correlation(self.vwap, adv20, 6)))) + (
                        0.6 * rank((((sma(self.close, 200) / 200) - self.open) * (self.close - self.open)))))

    def alpha037(self):
        return rank(correlation(delay(self.open - self.close, 1), self.close, 200)) + rank(self.open - self.close)

    def alpha038(self):
        inner = self.close / self.open
        inner = inner.replace([-np.inf, np.inf], 1).fillna(value=1)
        return -1 * rank(ts_rank(self.open, 10)) * rank(inner)

    def alpha039(self):
        adv20 = sma(self.volume, 20)
        return ((-1 * rank(delta_rate(self.close, 7) * (1 - rank(decay_linear((self.volume / adv20), 9))))) *
                (1 + rank(sma(self.returns, 250))))

    def alpha040(self):
        return -1 * rank(stddev(self.high, 10)) * correlation(self.high, self.volume, 10)

    def alpha041(self):
        return pow((self.high * self.low), 0.5) / self.vwap

    def alpha042(self):
        return rank((self.vwap - self.close)) / rank((self.vwap + self.close))

    def alpha043(self):
        adv20 = sma(self.volume, 20)
        return ts_rank(self.volume / adv20, 20) * ts_rank((-1 * delta(self.close, 7)), 8)

    def alpha044(self):
        df = correlation(self.high, self.volume, 5)
        return -1 * df

    def alpha045(self):
        df = correlation(self.close, self.volume, 2)
        return -1 * (rank(sma(delay(self.close, 5), 20) / self.close) * df *
                     rank(correlation(ts_sum(self.close, 5), ts_sum(self.close, 20), 4)))

    def alpha046(self):
        inner = ((delay(self.close, 20) - delay(self.close, 10)) / 10) - ((delay(self.close, 10) - self.close) / 10)
        alpha = (-1 * self.returns)
        alpha[inner < 0] = 1
        alpha[inner > 0.25] = -1
        return alpha

    def alpha047(self):
        adv20 = sma(self.volume, 20)
        return ((((rank((1 / self.close)) * self.volume) / adv20) * (
                (self.high * rank((self.high - self.close))) / (sma(self.high, 5) / 5))) - rank(
            (self.vwap - delay(self.vwap, 5))))

    def alpha049(self):
        inner = (((delay(self.close, 20) - delay(self.close, 10)) / 10) - ((delay(self.close, 10) - self.close) / 10))
        alpha = (-1 * delta_rate(self.close))
        alpha[inner < -0.1] = 1
        return alpha

    def alpha050(self):
        return -1 * ts_max(rank(correlation(rank(self.volume), rank(self.vwap), 5)), 5)

    def alpha051(self):
        inner = (((delay(self.close, 20) - delay(self.close, 10)) / 10) - ((delay(self.close, 10) - self.close) / 10))
        alpha = (-1 * delta_rate(self.close))
        alpha[inner < -0.05] = 1
        return alpha

    def alpha052(self):
        return (((-1 * delta(ts_min(self.low, 5), 5)) / self.low *
                 rank(((ts_sum(self.returns, 240) - ts_sum(self.returns, 20)) / 220))) * ts_rank(self.volume, 5))

    def alpha053(self):
        inner = (self.close - self.low).replace(0, 0.0001)
        return -1 * delta((((self.close - self.low) - (self.high - self.close)) / inner), 9)

    def alpha054(self):
        inner = (self.low - self.high).replace(0, -0.0001)
        return -1 * (self.low - self.close) * (self.open ** 5) / (inner * (self.close ** 5))

    def alpha055(self):
        divisor = (ts_max(self.high, 12) - ts_min(self.low, 12)).replace(0, 0.0001)
        inner = (self.close - ts_min(self.low, 12)) / divisor
        df = correlation(rank(inner), self.volume, 6)
        return -1 * df

    def alpha057(self):
        return -(1 * ((self.close / self.vwap) / decay_linear(rank(ts_argmax(self.close, 30)), 2)))

    def alpha060(self):
        divisor = (self.high - self.low).replace(0, 0.0001)
        inner = ((self.close - self.low) - (self.high - self.close)) * self.volume / divisor
        return - ((2 * scale(rank(inner))) - scale(rank(ts_argmax(self.close, 10))))

    def alpha061(self):
        adv180 = sma(self.volume, 180)
        return rank((self.vwap / ts_min(self.vwap, 16))) / rank(correlation(self.vwap, adv180, 18))

    def alpha062(self):
        adv20 = sma(self.volume, 20)
        return rank(correlation(self.vwap, sma(adv20, 6), 10)) * sign(self.open * 2 - (
                ((self.high + self.low) / 2) + self.high))

    def alpha064(self):
        adv120 = sma(self.volume, 120)
        return ((rank(
            correlation(sma(((self.open * 0.178404) + (self.low * (1 - 0.178404))), 13), sma(adv120, 13), 17)) / rank(
            delta(((((self.high + self.low) / 2) * 0.178404) + (self.vwap * (1 - 0.178404))), 4) / self.close)) * -1)

    def alpha065(self):
        adv60 = sma(self.volume, 60)
        return ((rank(
            correlation(((self.open * 0.00817205) + (self.vwap * (1 - 0.00817205))), sma(adv60, 9), 6)) / rank(
            (self.open - ts_min(self.open, 14)) / self.vwap)) * -1)

    def alpha066(self):
        return ((rank(decay_linear(delta_rate(self.vwap, 4), 7)) + ts_rank(
            decay_linear(((self.low - self.vwap) / (self.open - ((self.high + self.low) / 2))), 11),
            7)) * -1)

    def alpha068(self):
        adv15 = sma(self.volume, 15)

        return ((ts_rank(correlation(self.high, adv15, 9), 14) / rank(
            delta(((self.close * 0.518371) + (self.low * (1 - 0.518371))), 2)) * 14) * -1)

    def alpha071(self):
        adv180 = sma(self.volume, 180)
        p1 = ts_rank(decay_linear(correlation(ts_rank(self.close, 3), ts_rank(adv180, 12), 18), 4), 16)
        p2 = ts_rank(decay_linear((rank(((self.low + self.open) / (self.vwap + self.vwap))).pow(2)), 16), 4)
        return max(p1, p2)

    def alpha072(self):
        adv40 = sma(self.volume, 40)
        return (rank(decay_linear(correlation(((self.high + self.low) / 2), adv40, 9), 10)) / rank(
            decay_linear(correlation(ts_rank(self.vwap, 4), ts_rank(self.volume, 19), 7), 3)))

    def alpha073(self):
        p1 = rank(decay_linear(delta(self.vwap, 5), 3))
        p2 = ts_rank(decay_linear(((delta(((self.open * 0.147155) + (self.low * (1 - 0.147155))), 2) / (
                (self.open * 0.147155) + (self.low * (1 - 0.147155)))) * -1), 3), 17)
        return -1 * max(p1, p2)

    def alpha074(self):
        adv30 = sma(self.volume, 30)
        return ((rank(correlation(self.close, sma(adv30, 37), 15)) / rank(
            correlation(((self.high * 0.0261661) + (self.vwap * (1 - 0.0261661))), self.volume, 11))) * -1)

    def alpha075(self):
        adv50 = sma(self.volume, 50)
        return (rank(correlation(self.vwap, self.volume, 4)) / rank(
            correlation(self.low, adv50, 12)))

    def alpha077(self):
        adv40 = sma(self.volume, 40)
        p1 = rank(decay_linear(((((self.high + self.low) / 2) + self.high) - (self.vwap + self.high)), 20))
        p2 = rank(decay_linear(correlation(((self.high + self.low) / 2), adv40, 3), 6))
        return min(p1, p2)

    def alpha078(self):
        adv40 = sma(self.volume, 40)
        return (rank(
            correlation(ts_sum(((self.low * 0.352233) + (self.vwap * (1 - 0.352233))), 20), ts_sum(adv40, 20), 7)).pow(
            rank(correlation(self.vwap, self.volume, 6))))

    def alpha081(self):
        adv10 = sma(self.volume, 10)
        return ((rank(log(product(rank((rank(correlation(self.vwap, ts_sum(adv10, 50), 8)).pow(4))), 15))) / rank(
            correlation(rank(self.vwap), rank(self.volume), 5))) * -1)

    def alpha083(self):
        return ((rank(delay(((self.high - self.low) / (ts_sum(self.close, 5) / 5)), 2)) * rank(self.volume)) / (
                ((self.high - self.low) / (ts_sum(self.close, 5) / 5)) / (self.vwap - self.close)))

    def alpha084(self):
        return pow(ts_rank((self.vwap - ts_max(self.vwap, 15)), 21), delta_rate(self.close, 5))

    def alpha085(self):
        adv30 = sma(self.volume, 30)
        return (rank(correlation(((self.high * 0.876703) + (self.close * (1 - 0.876703))), adv30, 10)).pow(
            rank(correlation(ts_rank(((self.high + self.low) / 2), 4), ts_rank(self.volume, 10), 7))))

    def alpha086(self):
        adv20 = sma(self.volume, 20)

        return ((ts_rank(correlation(self.close, sma(adv20, 15), 6), 20) / rank(
            ((self.open + self.close) / (self.vwap + self.open))) * 20) * -1)

    def alpha088(self):
        adv60 = sma(self.volume, 60)
        p1 = rank(decay_linear((rank((self.open + self.low)) / (self.high + self.close)), 8))
        p2 = ts_rank(decay_linear(correlation(ts_rank(self.close, 8), ts_rank(adv60, 21), 8), 7), 3)
        return min(p1, p2)

    def alpha092(self):
        adv30 = sma(self.volume, 30)
        p1 = ts_rank(decay_linear(((((self.high + self.low) / 2) + self.close) / (self.low + self.open)), 15), 19)
        p2 = ts_rank(decay_linear(correlation(self.low, adv30, 8), 7), 7)
        return min(p1, p2)

    def alpha094(self):
        adv60 = sma(self.volume, 60)
        return ((rank((self.vwap - ts_min(self.vwap, 12)) / ts_min(self.vwap, 12)).pow(
            ts_rank(correlation(ts_rank(self.vwap, 20), ts_rank(adv60, 4), 18), 3)) * -1))

    def alpha095(self):
        adv40 = sma(self.volume, 40)

        return (rank((self.open - ts_min(self.open, 12))) * 12 / ts_rank(
            (rank(correlation(sma(((self.high + self.low) / 2), 19), sma(adv40, 19), 13)).pow(5)), 12))

    def alpha096(self):
        adv60 = sma(self.volume, 60)
        p1 = ts_rank(decay_linear(correlation(rank(self.vwap), rank(self.volume), 4), 4), 8)
        p2 = ts_rank(decay_linear(ts_argmax(correlation(ts_rank(self.close, 7), ts_rank(adv60, 4), 4), 13), 14), 13)
        return -1 * max(p1, p2)

    def alpha098(self):
        adv5 = sma(self.volume, 5)
        adv15 = sma(self.volume, 15)
        return (rank(decay_linear(correlation(self.vwap, sma(adv5, 26), 5), 7)) - rank(
            decay_linear(ts_rank(ts_argmin(correlation(self.open, adv15, 21), 9), 7), 8)))

    def alpha099(self):
        adv60 = sma(self.volume, 60)
        return ((rank(correlation(ts_sum(((self.high + self.low) / 2), 20), ts_sum(adv60, 20), 9)) / rank(
            correlation(self.low, self.volume, 6))) * -1)

    def alpha101(self):
        return (self.close - self.open) / ((self.high - self.low) + 0.001)

    def alpha102(self):
        """
        基于RSI的因子：
        使用14日RSI，返回信号为50 - RSI，数值越大表示股票处于超卖状态，
        可能预示回升机会。
        """
        rsi_val = rsi(self.close, window=14)
        return rank(50 - rsi_val)

    def alpha103(self):
        """
        均线交叉因子：
        计算5日均线与20日均线的差值占20日均线的比例，
        越高表明短期动能强于长期，倾向于看涨。
        """
        sma5 = sma(self.close, window=5)
        sma20 = sma(self.close, window=20)
        return rank((sma5 - sma20) / sma20)

    def alpha104(self):
        """
        OBV动量因子：
        计算OBV在10日内的变化率，反映资金流向的变化情况，
        较高的OBV增长率可能预示资金持续流入。
        """
        obv_val = obv(self.close, self.volume)
        return rank((obv_val - delay(obv_val, 10)) / (delay(obv_val, 10) + 1e-8))

    def alpha105(self):
        """
        ATR因子：
        计算14日ATR与收盘价的比率，用于衡量股票的波动性，
        波动率较高可能暗示短期价格波动较大。
        """
        atr_val = atr(self.high, self.low, self.close, window=14)
        return rank(atr_val / self.close)

    def alpha106(self):
        """
        相对强势因子：
        计算短期（5日）收益与长期（60日）收益之差，
        如果短期收益远高于长期收益，说明近期表现强势，
        则该因子信号较高。
        """
        short_ret = (self.close / delay(self.close, 5)) - 1
        long_ret = (self.close / delay(self.close, 60)) - 1
        return rank(short_ret - long_ret)

    # 1. 因子公式：
    # Var(Greater(Greater(Var(low,50), high), open), 30)
    def sub_alpha001(self):
        var_low_50 = ts_var(self.low, 50)
        part1 = max(var_low_50, self.high)  # Greater(Var(low,50), high)
        part2 = max(part1, self.open)  # Greater(..., open)
        alpha = ts_var(part2, 30)  # 最后滚动 30 日方差
        return alpha

    # 2. 因子公式：
    # Max((Min(Max(close,20) - 30, 20) + 100)/30, 20)
    def sub_alpha002(self):
        part1 = max(self.close, 20)  # Max(close,20)
        part2 = part1 - 30  # Max(close,20) - 30
        part3 = min(part2, 20)  # Min(...,20)
        part4 = (part3 + 100) / 30  # 加 100 后除以 30
        alpha = max(part4, 20)  # 最后与 20 比较取较大值
        return alpha

    # 3. 因子公式：
    # (Mad(high,50) + 0.5) * vwap / close
    def sub_alpha003(self):
        mad_high_50 = mad(self.high, 50)
        alpha = (mad_high_50 + 0.5) * self.vwap / self.close.replace(0, np.nan)
        return alpha

    # 4. 因子公式：
    # Ref(low,50)
    def sub_alpha004(self):
        return self.low.shift(50)

    # 5. 因子公式：
    # Min(high/close, 50) - Greater(-0.05/close, -10)
    def sub_alpha005(self):
        # 计算 high/close，避免除 0
        ratio = self.high / self.close.replace(0, np.nan)
        part1 = min(ratio, 50)  # Min(high/close,50)
        part2 = max(-0.05 / self.close.replace(0, np.nan), -10)  # Greater(-0.05/close, -10)
        alpha = part1 - part2
        return alpha

    # 6. 因子公式：
    # Delta(high,20) + high - 12
    def sub_alpha006(self):
        delta_high_20 = self.high.diff(20)  # Delta(high,20)
        alpha = delta_high_20 + self.high - 12
        return alpha

    # 7. 因子公式：
    # Less(Min(2*(vwap - volume), 30) + 30, 30*vwap/low) - 5
    def sub_alpha007(self):
        part1 = min(2 * (self.vwap - self.volume), 30)  # Min(2*(vwap-volume),30)
        part2 = part1 + 30  # +30
        part3 = 30 * self.vwap / self.low.replace(0, np.nan)  # 30*vwap/low
        less_val = min(part2, part3)  # Less(..., ...)
        alpha = less_val - 5
        return alpha

    # 8. 因子公式：
    # Corr( Greater( Greater(vwap, volume), Greater(close, Greater(Log(Var(volume,10)),10)) )/close, close, 10)
    def sub_alpha008(self):
        var_vol_10 = ts_var(self.volume, 10)
        log_var_vol_10 = log(var_vol_10.replace(0, np.nan))
        part_a = max(log_var_vol_10, 10)  # Greater(Log(Var(volume,10)), 10)
        part_b = max(self.close, part_a)  # Greater(close, part_a)
        part_c = max(self.vwap, self.volume)  # Greater(vwap, volume)
        part_d = max(part_c, part_b)  # Greater( part_c, part_b )
        expr = part_d / self.close.replace(0, np.nan)
        alpha = correlation(expr, self.close, 10)
        return alpha

    # 9. 因子公式：
    # |low - 30|
    def sub_alpha009(self):
        return abs(self.low - 30)

    # 10. 因子公式：
    # Max( 1 - Max( Corr(low, volume * Log(10^{-4} * Max(volume,10)), 10), 10), 30)
    def sub_alpha010(self):
        vol_max_10 = max(self.volume, 10)
        expr_inner = self.volume * log((10 ** (-4)) * vol_max_10)
        corr_val = correlation(self.low, expr_inner, 10)
        part_max = max(corr_val, 10)
        inner = 1 - part_max
        alpha = max(inner, 30)
        return alpha

    # 统一加权组合（权重根据你给出的示例）
    def alpha107(self):
        w1 = -0.0295
        w2 = 0.0515
        w3 = -0.0187
        w4 = 0.0245
        w5 = 0.0468
        w6 = -0.0997
        w7 = -0.0829
        w8 = -0.0279
        w9 = 0.1270
        w10 = 0.0312

        a1 = self.sub_alpha001()
        a2 = self.sub_alpha002()
        a3 = self.sub_alpha003()
        a4 = self.sub_alpha004()
        a5 = self.sub_alpha005()
        a6 = self.sub_alpha006()
        a7 = self.sub_alpha007()
        a8 = self.sub_alpha008()
        a9 = self.sub_alpha009()
        a10 = self.sub_alpha010()

        combo = (w1 * a1 + w2 * a2 + w3 * a3 + w4 * a4 + w5 * a5 +
                 w6 * a6 + w7 * a7 + w8 * a8 + w9 * a9 + w10 * a10)
        return combo

    def alpha108(self):
        # 表达式 1:
        # Add(Greater(Mul(Abs(Sub(Less(Constant(5.0),EMA($open,30)),$volume)),$vwap),Constant(2.0)),
        #     CSRank(Std(WMA($open,50),40)))
        ema_open_30 = self.open.ewm(span=30, adjust=False).mean()
        expr1_a = np.maximum(abs(np.minimum(5.0, ema_open_30) - self.volume) * self.vwap, 2.0)
        wma_open_50 = self.open.rolling(50, min_periods=min_periods_window(50)).apply(
            lambda x: np.dot(x, np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
            raw=True
        )
        expr1_b = stddev(wma_open_50, 40)
        expr1 = expr1_a + rank(expr1_b)

        # 表达式 2:
        # Mul(Constant(-0.5),Skew($high,20))
        expr2 = -0.5 * self.high.rolling(20, min_periods=min_periods_window(20)).skew()

        # 表达式 3:
        # Sub(Mad($close,10),Mad(Mean(Div(Mul(Greater($open,Constant(30.0)),
        #                       Abs(Log(Max($low,10)))),Constant(10.0)),10),40))
        expr3_part1 = mad(self.close, 10)
        expr3_part2 = np.maximum(self.open, 30.0)
        expr3_part3 = np.maximum(self.low, 10)
        expr3_part4 = abs(log(expr3_part3))
        expr3_part5 = expr3_part2 * expr3_part4 / 10.0
        expr3 = expr3_part1 - mad(sma(expr3_part5, 10), 40)

        # 表达式 4:
        # Log(Add($volume,Constant(30.0)))
        expr4 = log(self.volume + 30.0)

        # 表达式 5:
        # Sub(Log($high),Constant(-0.01))
        expr5 = log(self.high) + 0.01

        # 表达式 6:
        # CSRank(Sub(Mul(Greater(Greater(Greater(Constant(0.5),CSRank($high)),$open),
        #                        Min($volume,30)),Sub($high,Constant(-0.01))),
        #         EMA($open,50)))
        partA = np.maximum(0.5, rank(self.high))
        partB = np.maximum(partA, self.open)
        partC = np.minimum(self.volume, 30)
        partD = np.maximum(partB, partC)
        partE = self.high + 0.01
        expr6 = rank(partD * partE - self.open.ewm(span=50, adjust=False).mean())

        # 表达式 7:
        # Div($open,Sub($high,Less(Constant(0.5),Var(Sub($low,Constant(10.0)),40))))
        expr7 = self.open / (self.high - np.minimum(0.5, ts_var(self.low - 10.0, 40)))

        # 表达式 8:
        # Mul(Sub(Sub($volume,Constant(30.0)),
        #         Div($close,Add(Constant(10.0),$low))),
        #     Constant(0.5))
        expr8 = 0.5 * ((self.volume - 30.0) - (self.close / (10.0 + self.low)))

        # 表达式 9:
        # Corr(Add(Log($close),Constant(-5.0)),$volume,10)
        expr9 = correlation(log(self.close) - 5.0, self.volume, 10)

        # 表达式 10:
        # Mul(Constant(1.0),EMA(Less($open,Delta(Sub(Greater(Div(Add(Sub(Constant(1.0),$high),
        #                                                Constant(0.01)),
        #                                           Constant(-0.5)),
        #                                     Constant(-5.0)),$vwap),20)),50))
        part1 = 1.0 - self.high
        part2 = part1 + 0.01
        part3 = part2 / (-0.5)
        part4 = np.maximum(part3, -5.0)
        part5 = part4 - self.vwap
        part6 = delta(part5, 20)
        expr10 = np.minimum(self.open, part6).ewm(span=50, adjust=False).mean()

        # 按给定权重加权组合
        weights = [-0.0383512166990175, 0.01232909965937404, -0.012827311475833405,
                   -0.06923695174725919, -0.07668756805999075, 0.01656825731072831,
                   0.00838478305638833, -0.03179294072194176, -0.02086733688787641,
                   -0.022341622751366565]

        return (weights[0] * expr1 + weights[1] * expr2 + weights[2] * expr3 +
                weights[3] * expr4 + weights[4] * expr5 + weights[5] * expr6 +
                weights[6] * expr7 + weights[7] * expr8 + weights[8] * expr9 +
                weights[9] * expr10)

    def alpha109(self):
        """
        计算因子：PartialRatio($volume,240,"sum",80,85) * $s_dq_freeturnover
        其中：
         - PartialRatio: 在 240 日内，计算成交量在 80%～85% 分位数区间内的成交量之和占总成交量之比。
         - $s_dq_freeturnover: 这里近似定义为当前成交量与 240 日均成交量之比。
        """
        # 计算240日窗口内成交量在80%～85%分位数区间内的部分比率
        pr = self.volume.rolling(240, min_periods=240).apply(
            lambda x: np.sum(x[(x >= np.percentile(x, 80)) & (x <= np.percentile(x, 85))]) / np.sum(x),
            raw=True
        )
        # 近似自由换手率：当前成交量与240日均成交量之比
        turnover = self.volume / sma(self.volume, 240)
        return pr * turnover


if __name__ == '__main__':
    Alphas101.generate_alphas('a101')
