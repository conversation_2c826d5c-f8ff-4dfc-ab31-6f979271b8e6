import logging
import os

import alphalens
import fire
import pandas as pd
from alphalens.tears import create_full_tear_sheet
from autogluon.tabular import TabularPredictor

import configs
from configs import config_info
from ic_effective_backtrader_test import get_stock_data
from util import init_qlib, init_logger, prepare_add_features, remove_lastdays_from_data


class AdditionalModel:

    def train(self, end_year, with_main_features: bool = False):
        save_path = f'{config_info["market"]}_{end_year}'
        start_date = f'{end_year - config_info["train_year"]}0101'
        end_date = f'{end_year}1231'
        train_data = prepare_add_features(start_date, end_date, with_main_features)
        train_data = remove_lastdays_from_data(train_data)
        last_day = max(train_data.index.get_level_values(0).unique())

        presets = config_info['presets']
        tuned_data = None
        holdout_frac = 0.2

        hyperparameters = {'GBM': [
            'GBMLarge',
        ], 'XGB': {}}

        logging.getLogger().info(
            '%s stocks in the training' % len(train_data.index.get_level_values(1).unique().tolist()))
        logging.getLogger().info(f'start at {start_date}, end at {end_date}， last date is {last_day}')

        TabularPredictor(label=f'return_{config_info["period_n"]}D', path=f'add_models/{save_path}',
                         sample_weight='weight' if with_main_features else None).fit(
            train_data,
            tuned_data,
            presets=presets,
            keep_only_best=True,
            save_space=True,
            hyperparameters=hyperparameters,
            ag_args_ensemble={'num_folds_parallel': 3},
            num_gpus=0 if config_info['cpu'] else 1,
            holdout_frac=holdout_frac,
            feature_prune_kwargs={'force_prune': True} if config_info['auto_feature_select'] else None,
            ag_args_fit={"ag.max_memory_usage_ratio": 0.8},
            hyperparameter_tune_kwargs='auto' if config_info['hyperparameter_tune_kwargs'] else None)

    def get_close_price(self, start, end=None):
        start = f'{start}0101'
        if end is not None:
            end = f'{end}1231'
        else:
            end = f'{start}1231'
        stock_data = get_stock_data(configs.convert_to_csi_code(config_info['market']),
                                    pd.to_datetime(start) - pd.Timedelta(days=20),
                                    pd.to_datetime(end) + pd.Timedelta(days=20))

        # 更新索引
        new_index = [(x[2:], y) for x, y in stock_data.index]
        stock_data.index = pd.MultiIndex.from_tuples(new_index, names=stock_data.index.names)

        # 获取并排序收盘价
        close = stock_data[['close']].sort_index()
        close = close.loc[~close.index.duplicated(keep='first')]

        # 筛选日期范围内的数据
        close = close.loc[(close.index.get_level_values(1) >= start) & (close.index.get_level_values(1) <= end)]

        # 重置索引并设置新的索引
        close = close.reset_index()
        close['datetime'] = pd.to_datetime(close['datetime'])
        close = close.set_index(['datetime', 'instrument']).sort_index()

        # 确保索引没有重复值
        close = close[~close.index.duplicated(keep='first')]

        # 检查并设置索引的频率
        dates = close.index.get_level_values('datetime')
        inferred_freq = pd.infer_freq(dates)
        print("Inferred frequency of close index:", inferred_freq)

        if inferred_freq is None:
            desired_freq = 'B'
            all_dates = pd.date_range(start=dates.min(), end=dates.max(), freq=desired_freq)
            instruments = close.index.get_level_values('instrument').unique()
            full_index = pd.MultiIndex.from_product([all_dates, instruments], names=['datetime', 'instrument'])
            close = close.reindex(full_index).ffill().bfill()

            return close

    def save_predict_data(self, scores, path):
        scores.reset_index(inplace=True)
        scores.rename(columns={f'return_{config_info["period_n"]}D': 'return', 'asset': 'sec_id'}, inplace=True)
        if os.path.exists(path):
            df = pd.read_csv(path, parse_dates=['date'], dtype={'sec_id': str})
            df = pd.merge(df, scores, on=['date', 'sec_id'], how='outer', suffixes=('', '_new'))

            # 删除重复的列并保留新的'scores'中的值
            for col in scores.columns:
                if col != 'date' and col != 'sec_id':
                    df[col] = df[f'{col}_new'].combine_first(df[col])
                    df.drop(columns=[f'{col}_new'], inplace=True)
            scores = df
        scores.to_csv(path, index=False)

    def evaluate_features(self, start, end):
        start_date = f'{start}0101'
        end_date = f'{end}1231'
        test_data = prepare_add_features(start_date, end_date, with_main_features=False)
        test_data = test_data.sort_index()
        test_data.dropna(inplace=True)
        close = self.get_close_price(start, end)
        common_index = test_data.index.intersection(close.index)
        test_data = test_data.loc[common_index]
        close = close.loc[common_index]
        close_series = close.pivot_table(values='close', index='datetime', columns='instrument')
        for col in test_data.columns:
            print(f'-----------------{col}---------------')
            try:
                factors = alphalens.utils.get_clean_factor_and_forward_returns(
                    factor=test_data[col],
                    prices=close_series,
                    bins=None,
                    periods=(1, 5, 10),
                    quantiles=5,
                    max_loss=0.50
                )

                ics = alphalens.performance.mean_information_coefficient(factors)
                print(ics)
                create_full_tear_sheet(factors, long_short=False, by_group=False)
            except Exception as e:
                print(e)

    def evaluate(self, year, with_main_features: bool = False):
        start_date = f'{year}0101'
        end_date = f'{year}1231'

        test_data = prepare_add_features(start_date, end_date, with_main_features)
        test_data = test_data.sort_index(level=0, ascending=False)
        test_data.dropna(inplace=True)

        save_path = f'{config_info["market"]}_{year - 1}'
        pred = TabularPredictor.load(f'add_models/{save_path}')
        eval_result = pred.evaluate(test_data)
        logging.getLogger().info(eval_result)

        close = self.get_close_price(year)
        scores = pred.predict(test_data)
        scores = pd.DataFrame(scores, index=scores.index, columns=[f'return_{config_info["period_n"]}D'])
        scores = scores.sort_index()
        scores = scores[~scores.index.duplicated(keep='first')]

        common_index = scores.index.intersection(close.index)
        scores = scores.loc[common_index]
        close = close.loc[common_index]

        i = scores[f'return_{config_info["period_n"]}D']
        close_series = close.pivot_table(values='close', index='datetime', columns='instrument')
        factors = alphalens.utils.get_clean_factor_and_forward_returns(
            factor=i,
            prices=close_series,
            bins=None,
            periods=(1, 5, 10),
            quantiles=5,
            max_loss=0.50
        )

        ics = alphalens.performance.mean_information_coefficient(factors)
        print(ics)
        create_full_tear_sheet(factors, long_short=False, by_group=False)

        self.save_predict_data(scores, f'{config_info["market"]}_assist_pred_data.csv')


if __name__ == '__main__':
    init_qlib()
    init_logger()
    fire.Fire(AdditionalModel)
