import fire
from alphalens.tears import create_returns_tear_sheet
from boruta import BorutaPy
from sklearn.ensemble import RandomForestRegressor
from sklearn.utils import check_random_state

import alpha_101
import alpha_191
import alpha_300
from alphabase import Alphas
from data_preparer import alpha_generator_with_melt
from util import *


class BorutaPyForLGB(BorutaPy):
    def __init__(self, estimator, n_estimators=1000, perc=100, alpha=0.05,
                 two_step=True, max_iter=100, random_state=None, verbose=0):
        super().__init__(estimator, n_estimators, perc, alpha,
                         two_step, max_iter, random_state, verbose)
        self._is_lightgbm = 'lightgbm' in str(type(self.estimator))

    def _fit(self, X, y):
        # check input params
        self._check_params(X, y)

        if not isinstance(X, np.ndarray):
            X = self._validate_pandas_input(X)
        if not isinstance(y, np.ndarray):
            y = self._validate_pandas_input(y)

        self.random_state = check_random_state(self.random_state)
        # setup variables for Boruta
        n_sample, n_feat = X.shape
        _iter = 1
        # holds the decision about each feature:
        # 0  - default state = tentative in original code
        # 1  - accepted in original code
        # -1 - rejected in original code
        dec_reg = np.zeros(n_feat, dtype=np.int)
        # counts how many times a given feature was more important than
        # the best of the shadow features
        hit_reg = np.zeros(n_feat, dtype=np.int)
        # these record the history of the iterations
        imp_history = np.zeros(n_feat, dtype=np.float)
        sha_max_history = []

        # set n_estimators
        if self.n_estimators != 'auto':
            self.estimator.set_params(n_estimators=self.n_estimators)

        # main feature selection loop
        while np.any(dec_reg == 0) and _iter < self.max_iter:
            # find optimal number of trees and depth
            if self.n_estimators == 'auto':
                # number of features that aren't rejected
                not_rejected = np.where(dec_reg >= 0)[0].shape[0]
                n_tree = self._get_tree_num(not_rejected)
                self.estimator.set_params(n_estimators=n_tree)

            # make sure we start with a new tree in each iteration
            if self._is_lightgbm:
                self.estimator.set_params(random_state=self.random_state.randint(0, 10000))
            else:
                self.estimator.set_params(random_state=self.random_state)

            # add shadow attributes, shuffle them and train estimator, get imps
            cur_imp = self._add_shadows_get_imps(X, y, dec_reg)

            # get the threshold of shadow importances we will use for rejection
            imp_sha_max = np.percentile(cur_imp[1], self.perc)

            # record importance history
            sha_max_history.append(imp_sha_max)
            imp_history = np.vstack((imp_history, cur_imp[0]))

            # register which feature is more imp than the max of shadows
            hit_reg = self._assign_hits(hit_reg, cur_imp, imp_sha_max)

            # based on hit_reg we check if a feature is doing better than
            # expected by chance
            dec_reg = self._do_tests(dec_reg, hit_reg, _iter)

            # print out confirmed features
            if self.verbose > 0 and _iter < self.max_iter:
                self._print_results(dec_reg, _iter, 0)
            if _iter < self.max_iter:
                _iter += 1

        # we automatically apply R package's rough fix for tentative ones
        confirmed = np.where(dec_reg == 1)[0]
        tentative = np.where(dec_reg == 0)[0]
        # ignore the first row of zeros
        tentative_median = np.median(imp_history[1:, tentative], axis=0)
        # which tentative to keep
        tentative_confirmed = np.where(tentative_median
                                       > np.median(sha_max_history))[0]
        tentative = tentative[tentative_confirmed]

        # basic result variables
        self.n_features_ = confirmed.shape[0]
        self.support_ = np.zeros(n_feat, dtype=np.bool)
        self.support_[confirmed] = 1
        self.support_weak_ = np.zeros(n_feat, dtype=np.bool)
        self.support_weak_[tentative] = 1

        # ranking, confirmed variables are rank 1
        self.ranking_ = np.ones(n_feat, dtype=np.int)
        # tentative variables are rank 2
        self.ranking_[tentative] = 2
        # selected = confirmed and tentative
        selected = np.hstack((confirmed, tentative))
        # all rejected features are sorted by importance history
        not_selected = np.setdiff1d(np.arange(n_feat), selected)
        # large importance values should rank higher = lower ranks -> *(-1)
        imp_history_rejected = imp_history[1:, not_selected] * -1

        # update rank for not_selected features
        if not_selected.shape[0] > 0:
            # calculate ranks in each iteration, then median of ranks across feats
            iter_ranks = self._nanrankdata(imp_history_rejected, axis=1)
            rank_medians = np.nanmedian(iter_ranks, axis=0)
            ranks = self._nanrankdata(rank_medians, axis=0)

            # set smallest rank to 3 if there are tentative feats
            if tentative.shape[0] > 0:
                ranks = ranks - np.min(ranks) + 3
            else:
                # and 2 otherwise
                ranks = ranks - np.min(ranks) + 2
            self.ranking_[not_selected] = ranks
        else:
            # all are selected, thus we set feature supports to True
            self.support_ = np.ones(n_feat, dtype=np.bool)

        self.importance_history_ = imp_history

        # notify user
        if self.verbose > 0:
            self._print_results(dec_reg, _iter, 1)
        return self


def do_feature_selection(alpha_type, period_n, train_end_date):
    df = load_stock_data(alpha_type, config_info['market'])
    df = df.loc[df.index.get_level_values(0) >= pd.to_datetime('20100101'), :]
    ic_table = pd.read_csv(f'{alpha_type}_{config_info["market"]}_ic.csv', index_col=0)
    factor_names = ic_table.index.tolist()
    train_data = prepare_data_for_model(df, factor_names, period_n, config_info['start_date'],
                                        train_end_date)
    rf = RandomForestRegressor(max_depth=5, random_state=1, n_jobs=-1)
    # rf = LGBMRegressor(num_boost_round=100)
    selector = BorutaPy(rf, n_estimators='auto',
                        verbose=2, random_state=1)

    returns = train_data['return']
    train_data.drop(columns=['return'], inplace=True)
    train_data = train_data.loc[:, (train_data.max() < 1000000)]
    selector.fit(train_data.values, returns.values)
    return selector.ranking_


def process_alpha_select(start=None, end=None):
    df_all, stock_data = Alphas.get_stocks_data_by_date(end, start)
    df_all.set_index(['date', 'sec_id'], inplace=True)
    df_all.rename(columns={'change': 'return'}, inplace=True)
    for alpha_name in os.listdir('feature'):
        if not alpha_name.endswith('.pkl'):
            continue
        factors = pd.read_pickle(f'feature/{alpha_name}')
        factors['date'] = pd.to_datetime(factors['date'])
        factors.set_index(['date', 'sec_id'], inplace=True)
        df_all = factors.merge(df_all, left_index=True, right_index=True, how='inner')
    df_all = refine_stock_df(df_all)
    print('mean return: ', df_all['return_10D'].mean())
    for alpha_name in os.listdir('feature'):
        alpha_name = alpha_name.replace('.pkl', '')
        min_a = min(df_all[alpha_name])
        max_a = max(df_all[alpha_name])
        for j in range(0, 20):
            step = (max_a - min_a) / 20
            i = min_a + j * step
            selected = df_all[(df_all[alpha_name] >= i) & (df_all[alpha_name] <= (i + step))]['return_10D']
            mean_a = selected.mean()
            if mean_a > 0.01:
                print(f'{alpha_name} range: {i:.2f} - {i + step:.2f}, mean: {mean_a}, count: {selected.count()}')

    print(selected['return_10D'].mean(), selected['return_10D'].count())


def research_alpha_by_data(alpha_name, df_all, stock_data):
    if not os.path.exists('feature'):
        os.mkdir('feature')

    alpha_classes = [alpha_101.Alphas101, alpha_191.Alphas191, alpha_300.ClaudeAlphas]
    for alpha_class in alpha_classes:
        alpha_instance = alpha_class(stock_data)
        methods = alpha_class.get_alpha_methods(alpha_instance)
        if alpha_name in methods:
            alpha_generator_with_melt(alpha_instance, alpha_class, alpha_name)
            break
    else:
        return None

    instruments = get_instrument_list()
    df_all = df_all[df_all['sec_id'].isin(instruments)]
    factors = pd.read_pickle(f'feature/{alpha_name}.pkl')
    factors['date'] = pd.to_datetime(factors['date'])
    factors.set_index(['date', 'sec_id'], inplace=True)
    df_all.set_index(['date', 'sec_id'], inplace=True)
    df_all.rename(columns={'change': 'return'}, inplace=True)
    df_all = factors.merge(df_all, left_index=True, right_index=True, how='inner')
    df_all = refine_stock_df(df_all)
    returns = df_all.loc[:, ['close']].copy()
    returns = returns.pivot_table(index='date', columns='sec_id', values='close')
    print(df_all[alpha_name].describe())
    try:
        factors = alphalens.utils.get_clean_factor_and_forward_returns(df_all[alpha_name], returns,
                                                                       bins=None,
                                                                       periods=(1, 5, 10), quantiles=5,
                                                                       max_loss=0.35)
    except MaxLossExceededError as e:
        print(e)
        print('fall back to binn mode')
        min_f, max_f = min(df_all[alpha_name]), max(df_all[alpha_name])
        try:
            factors = alphalens.utils.get_clean_factor_and_forward_returns(df_all[alpha_name], returns,
                                                                           bins=np.linspace(min_f, max_f, 5),
                                                                           periods=(1, 5, 10), quantiles=None,
                                                                           max_loss=0.52)
        except MaxLossExceededError as e:
            factors = alphalens.utils.get_clean_factor_and_forward_returns(df_all[alpha_name], returns,
                                                                           bins=3,
                                                                           periods=(1, 5, 10), quantiles=None,
                                                                           max_loss=0.52)
            ic = perf.mean_information_coefficient(factors)
            return ic, factors

    best_means = 0
    best_range = ()
    for j in range(1, 6):
        factor_quantile = factors[factors['factor_quantile'] == j]
        if len(factor_quantile) == 0:
            continue
        min_f = min(factor_quantile['factor'])
        max_f = max(factor_quantile['factor'])
        selected = df_all[(df_all[alpha_name] >= min_f) & (df_all[alpha_name] <= max_f)]['return_10D']
        mean_a = selected.mean()
        if mean_a > best_means:
            best_means = mean_a
            best_range = (min_f, max_f)
    if best_means > 0:
        print(f'{alpha_name} range: {best_range[0]:.2f} - {best_range[1]:.2f}, mean: {best_means}')

    ic = perf.mean_information_coefficient(factors)
    return ic, factors


def compute_alpha_corr(alpha1, alpha2):
    factors1 = pd.read_pickle(f'feature/{alpha1}.pkl')
    factors1['date'] = pd.to_datetime(factors1['date'])
    factors1.set_index(['date', 'sec_id'], inplace=True)

    factors2 = pd.read_pickle(f'feature/{alpha2}.pkl')
    factors2['date'] = pd.to_datetime(factors2['date'])
    factors2.set_index(['date', 'sec_id'], inplace=True)
    combined = factors1.merge(factors2, left_index=True, right_index=True, how='inner')
    correlation = combined[alpha1].corr(combined[alpha2])
    return correlation


def compute_feature_ic(alpha_name, start, end):
    start = str(start)
    end = str(end)
    df_all, stock_data = Alphas.get_stocks_data_by_date(end, start)
    ic, factors = research_alpha_by_data(alpha_name, df_all, stock_data)
    return ic, factors

def analyze_features_correlations(start_date, end_date, corr_threshold=0.95, prefix=''):
    """
    加载数据后，对所有数值特征以及目标变量("return")做相关性分析，
    同时根据以下两个规则给出建议剔除的特征列表：
        1. 与目标变量("return")的相关性绝对值低于 target_threshold 的特征；
        2. 特征间两两相关性绝对值超过 corr_threshold，保留与"return"相关性较高的特征，其它作为备选剔除项。
    返回建议去掉的特征列表。
    """

    # 加载数据
    df = load_stock_data(config_info['alpha_type'], config_info['market'], prefix)
    feature_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])
    df = prepare_data_for_model(df, feature_names, config_info['period_n'], start_date, end_date)
    if 'return' not in df.columns:
        print("数据中不存在 'return' 列，无法进行目标相关性分析。")
        return []

    # 选取所有数值特征
    numeric_df = df.select_dtypes(include=[np.number])
    features = numeric_df.columns.tolist()

    # 设置阈值，可根据实际需要修改
    target_threshold = 0.0  # 与目标相关性太低的特征

    # 计算相关矩阵
    corr_matrix = numeric_df.corr().abs()

    # 1. 首先筛选出那些与 "return" 相关性较低的特征
    low_target_features = []
    for feat in features:
        if feat == 'return':
            continue
        corr_with_target = corr_matrix.at[feat, 'return']
        if abs(corr_with_target) < target_threshold:
            low_target_features.append(feat)
    print("与 'return' 相关性低（阈值 < {}）的特征：{}".format(target_threshold, low_target_features))

    # 2. 对于高度相关的特征组，去掉与 'return' 相关性较低的那个
    redundant_features = set()
    # 列表仅包括除 "return" 以外的特征
    feature_list = [feat for feat in features if feat != 'return']
    for i in range(len(feature_list)):
        for j in range(i + 1, len(feature_list)):
            fi = feature_list[i]
            fj = feature_list[j]
            corr_ij = corr_matrix.at[fi, fj]
            if abs(corr_ij) > corr_threshold:
                # 比较两者与 return 的相关性，剔除相关性较低的那个
                corr_fi = abs(corr_matrix.at[fi, 'return'])
                corr_fj = abs(corr_matrix.at[fj, 'return'])
                if corr_fi < corr_fj:
                    redundant_features.add(fi)
                else:
                    redundant_features.add(fj)

    print("特征间高度相关(阈值 > {})建议剔除的特征：{}".format(corr_threshold, list(redundant_features)))

    # 合并两个策略的建议
    drop_features = list(set(low_target_features) | redundant_features)
    print("建议剔除的特征列表：", drop_features)
    with open('feature_abandon', 'w') as f:
        for feature in drop_features:
            f.write(f"{feature}\n")
    return drop_features


if __name__ == '__main__':
    init_qlib()
    init_logger()
    fire.Fire({
        'compute_alpha_corr': compute_alpha_corr,
        'compute_feature_ic': compute_feature_ic,
        'gen_abandon': analyze_features_correlations
    })
