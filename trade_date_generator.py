import os
import sys

import pandas as pd

import util


def get_index_code(directory):
    """获取目录对应的指数代码"""
    if directory == 'runtime':
        return '000300'
    elif len(directory) == 6 and directory.isdigit():
        return directory
    else:
        return None  # 无对应指数代码


def load_data(index_dirs):
    """加载所有指数的数据"""
    index_data = {}
    for index_code, dir_name in index_dirs.items():
        csv_file = os.path.join(dir_name, f'{index_code}.csv')
        if not os.path.exists(csv_file):
            print(f"警告：在目录 {dir_name} 中未找到文件 {index_code}.csv，跳过该指数。")
            continue
        df = pd.read_csv(csv_file, parse_dates=['date'])
        df.set_index('date', inplace=True)
        index_data[index_code] = df
    return index_data


def calculate_performance(index_data):
    """计算每个指数的周和月收益，以及综合表现"""
    for index_code, df in index_data.items():
        df.sort_index(inplace=True)
        # 计算每日收益率
        df['week_return'] = df['close'].pct_change(5)
        weights = [1]
        df['performance'] = 0
        for i in range(len(weights)):
            df['performance'] += weights[i] * df['week_return'].shift(i + 1)
        index_data[index_code] = df
    return index_data


def assign_trading_days(index_data, index_dirs):
    """根据每日的表现为每个指数分配交易日期"""
    # 获取所有指数的日期，并取并集
    all_dates = pd.to_datetime([])
    for df in index_data.values():
        all_dates = all_dates.union(df.index)
    all_dates = all_dates.sort_values()

    date_to_index = {}
    for date in all_dates:
        performances = {}
        for index_code, df in index_data.items():
            if date in df.index:
                perf = df.loc[date, 'performance']
                if pd.notnull(perf):
                    performances[index_code] = perf
        if performances:
            # 选择当天表现最好的指数
            best_index = max(performances, key=performances.get)
            date_to_index[date] = best_index
        else:
            print(f"警告：日期 {date.strftime('%Y-%m-%d')} 没有可用的指数数据。")

    # 确保每个日期只分配给一个指数
    index_dates = {index_code: [] for index_code in index_dirs.keys()}
    for date, index_code in date_to_index.items():
        if index_code in index_dates:
            index_dates[index_code].append(date.strftime('%Y-%m-%d'))
        else:
            # 如果最佳指数的目录不存在，跳过该日期
            continue

    # 输出 trade_date.txt 文件
    for index_code, dates in index_dates.items():
        dir_name = index_dirs[index_code]
        output_file = os.path.join(dir_name, 'trade_date.txt')
        with open(output_file, 'w') as f:
            for date_str in dates:
                f.write(date_str + '\n')
    print('trade_date.txt 文件已生成。')
    # 获取最后一个交易日及其分配的指数
    last_date = max(date_to_index.keys())
    last_index = date_to_index[last_date]
    return last_date.strftime('%Y-%m-%d'), last_index


def main():
    # 获取命令行参数中的目录列表
    directories = sys.argv[1:]
    if not directories:
        print("错误：未提供任何目录参数。")
        return

    index_dirs = {}
    for d in directories:
        if not os.path.isdir(d):
            print(f"错误：{d} 不是有效的目录，已跳过。")
            continue
        index_code = get_index_code(d)
        if index_code:
            index_dirs[index_code] = d
        else:
            print(f"信息：目录 {d} 没有对应的指数代码，已跳过。")

    if not index_dirs:
        print("错误：没有找到有效的指数目录。")
        return

    # 加载数据
    index_data = load_data(index_dirs)
    if not index_data:
        print("错误：没有加载到任何指数数据。")
        return

    # 计算表现
    index_data = calculate_performance(index_data)

    # 分配交易日期并输出结果
    last_date, last_index = assign_trading_days(index_data, index_dirs)

    util.notify_by_email(f'{last_date} {last_index}',
                         f'{last_date} {last_index}',
                         force_send=True)

if __name__ == '__main__':
    main()
