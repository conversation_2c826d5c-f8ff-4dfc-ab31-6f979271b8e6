import fire
import qlib
from qlib.data import D
import numpy as np
import pandas as pd
from datetime import datetime, timedelta


def get_stock_monthly_return(stock_code, start_date, end_date):
    """
    获取股票在指定月份的收益率，使用当月第一个交易日的开盘价和最后一个交易日的收盘价，
    以尽量模拟 fake_index_data.py 中的再平衡逻辑。
    """
    # 获取本月交易日历
    trading_days = D.calendar(start_time=start_date, end_time=end_date)
    if trading_days is  None or len(trading_days) == 0:
        return np.nan
    first_trading_day = trading_days[0].strftime('%Y-%m-%d')
    last_trading_day = trading_days[-1].strftime('%Y-%m-%d')

    # 获取第一个交易日的开盘价
    df_open = D.features([stock_code], ['$open'], start_time=first_trading_day, end_time=first_trading_day)
    # 获取最后一个交易日的收盘价
    df_close = D.features([stock_code], ['$close'], start_time=last_trading_day, end_time=last_trading_day)

    if df_open.empty or df_close.empty:
        return np.nan

    open_price = df_open.iloc[0]['$open']
    close_price = df_close.iloc[0]['$close']

    if pd.isnull(open_price) or pd.isnull(close_price) or open_price == 0:
        return np.nan

    # 计算单只股票的月度收益率
    monthly_return = (close_price - open_price) / open_price
    return monthly_return


def calculate_annual_equal_weight_return(index_code, start_year, end_year):
    annual_returns = {}

    for year in range(start_year, end_year + 1):
        monthly_returns = []

        for month in range(1, 13):
            # 构造本月的起始日期
            try:
                month_start = datetime(year, month, 1)
            except Exception:
                continue
            if month_start > datetime.today():
                continue

            # 计算本月的最后一天（取月份末日，如果超出今日则取今日）
            if month == 12:
                next_month = datetime(year + 1, 1, 1)
            else:
                next_month = datetime(year, month + 1, 1)
            month_end = next_month - timedelta(days=1)
            if month_end > datetime.today():
                month_end = datetime.today()

            start_date_str = month_start.strftime('%Y-%m-%d')
            end_date_str = month_end.strftime('%Y-%m-%d')

            # 获取当月的成分股列表
            components = D.list_instruments(D.instruments(market=index_code),
                                            start_time=start_date_str, end_time=end_date_str)
            if not components:
                continue

            stock_returns = []
            for stock in components:
                ret = get_stock_monthly_return(stock, start_date_str, end_date_str)
                if not np.isnan(ret):
                    stock_returns.append(ret)
            if stock_returns:
                # 计算等权重月度收益（简单平均）
                avg_monthly_return = np.mean(stock_returns)
                monthly_returns.append(avg_monthly_return)

        if monthly_returns:
            # 累计乘积计算年度总收益
            cumulative_return = np.prod([1 + r for r in monthly_returns]) - 1
            annual_returns[year] = cumulative_return
        else:
            annual_returns[year] = np.nan

    return annual_returns


def main(index_code, start_year=2020, end_year=2025):
    qlib.init(provider_uri='~/qlib_bin_today')
    annual_returns = calculate_annual_equal_weight_return(index_code, start_year, end_year)
    print("每年等权重平均收益（基于月初开盘和月末收盘）：")
    total = 1
    for year, ret in annual_returns.items():
        if not np.isnan(ret):
            print(f"{year}年: {ret * 100:.2f}%")
            total *= (1 + ret)
        else:
            print(f"{year}年: 数据不可用")
    print(f"累计收益：{(total - 1) * 100:.2f}%")


if __name__ == '__main__':
    fire.Fire(main)
