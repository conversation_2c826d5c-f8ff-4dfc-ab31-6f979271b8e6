import os
import time
from datetime import datetime, timedelta
from functools import wraps

import fire
import pandas as pd
import tushare as ts
from qlib.data import D
from tqdm import tqdm

from configs import convert_to_market_code, config_info
from util import init_qlib


def partial_cache_data(key_param_names):
    """
    数据缓存装饰器(带增量更新+覆盖区间逻辑)。
    key_param_names: tuple/list，需要用于生成缓存文件名的关键参数名称。例如 ('ts_code', )
    """

    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # 1) 根据 key_param_names 确定缓存文件和 coverage 文件
            cache_keys = []
            for param_name in key_param_names:
                if param_name in kwargs:
                    cache_keys.append(f"{param_name}_{kwargs[param_name]}")
                else:
                    raise ValueError(
                        f"Key param '{param_name}' not found in kwargs. "
                        f"Please call with keyword args."
                    )

            cache_file_name = func.__name__ + '_' + '_'.join(cache_keys) + '.csv'
            cache_file = os.path.join(self.cache_dir, cache_file_name)

            coverage_file_name = func.__name__ + '_' + '_'.join(cache_keys) + '_coverage.csv'
            coverage_file = os.path.join(self.cache_dir, coverage_file_name)

            # 2) 读取缓存数据
            os.makedirs(os.path.dirname(cache_file), exist_ok=True)
            if os.path.exists(cache_file):
                cached_df = pd.read_csv(cache_file, dtype=str)
            else:
                cached_df = pd.DataFrame()

            # 3) 如果用户没传日期(start_date, end_date)，或是底层函数无日期逻辑，那就直接简单缓存。
            start_date = kwargs.get('start_date')
            end_date = kwargs.get('end_date')

            # a) 检查是否在 cached_df 里有 'trade_date' 列
            has_trade_date_in_cache = ('trade_date' in cached_df.columns)
            # b) 如果本次**根本没传** start_date / end_date，我们也当做不走 coverage
            no_date_range_requested = (start_date is None or end_date is None)

            # == 如果不走覆盖逻辑，就简单做：直接调用 func，合并数据，返回 ==
            if not has_trade_date_in_cache or no_date_range_requested:
                # 先调接口获取最新数据
                if not os.path.exists(cache_file):
                    cached_df = func(self, *args, **kwargs)
                    cached_df.to_csv(cache_file, index=False)
                return cached_df

            # 4) 若确实需要"按日期增量"，我们再读 coverage 文件(如果有)
            if os.path.exists(coverage_file):
                coverage_df = pd.read_csv(coverage_file, dtype=str)
            else:
                coverage_df = pd.DataFrame(columns=['start_date', 'end_date', 'has_data'])

            # 4.1) 如果 coverage_df 还没记录，但 cached_df 里已经有数据，就自动初始化
            if coverage_df.empty and not cached_df.empty:
                min_cached_date = cached_df['trade_date'].min()
                max_cached_date = cached_df['trade_date'].max()
                # 这里改用 pd.concat，而不是 DataFrame.append
                init_row = pd.DataFrame([{
                    'start_date': str(min_cached_date),
                    'end_date': str(max_cached_date),
                    'has_data': 'True'
                }])
                coverage_df = pd.concat([coverage_df, init_row], ignore_index=True)

                coverage_df.drop_duplicates(subset=['start_date', 'end_date', 'has_data'], inplace=True)
                coverage_df.to_csv(coverage_file, index=False)

            # 5) 计算真正需要"新拉取"的 missing_intervals
            req_start = str(start_date)
            req_end = str(end_date)

            # 把用户的总区间先做成一个列表(可能后面要拆分)
            intervals_to_check = [(req_start, req_end)]

            def subtract_interval(base: (str, str), cut: (str, str)):
                """
                从区间base里减去区间cut，返回若干子区间不重叠部分(list of (start,end))
                """
                bstart, bend = base
                cstart, cend = cut
                if cend < bstart or cstart > bend:
                    # 没交集
                    return [base]
                # 如果有部分重叠
                results = []
                if cstart > bstart:
                    results.append(
                        (bstart, (datetime.strptime(cstart, "%Y%m%d") - timedelta(days=1)).strftime("%Y%m%d"))
                    )
                if cend < bend:
                    results.append(
                        ((datetime.strptime(cend, "%Y%m%d") + timedelta(days=1)).strftime("%Y%m%d"), bend)
                    )
                # 如果 cut 完全覆盖 base，则 results 为空
                return [(s, e) for (s, e) in results if s <= e]

            # 先去掉 coverage_df 里 has_data=True 的区间
            covered_intervals = coverage_df[coverage_df['has_data'] == 'True'][
                ['start_date', 'end_date']
            ].values.tolist()
            for (cs, ce) in covered_intervals:
                new_list = []
                for (bs, be) in intervals_to_check:
                    sub_parts = subtract_interval((bs, be), (cs, ce))
                    new_list.extend(sub_parts)
                intervals_to_check = new_list
                if not intervals_to_check:
                    break

            # 再去掉 coverage_df 里 has_data=False 的区间(已经确认没数据，无需再次拉)
            nodata_intervals = coverage_df[coverage_df['has_data'] == 'False'][
                ['start_date', 'end_date']
            ].values.tolist()
            for (cs, ce) in nodata_intervals:
                new_list = []
                for (bs, be) in intervals_to_check:
                    sub_parts = subtract_interval((bs, be), (cs, ce))
                    new_list.extend(sub_parts)
                intervals_to_check = new_list
                if not intervals_to_check:
                    break

            # 6) 调用 func 拉取 intervals_to_check 里的数据
            new_data_frames = []
            coverage_updates = []  # 用于记录调用 func 后得到的数据覆盖情况

            for (ms, me) in intervals_to_check:
                if ms > me:
                    continue
                df_new = func(self, **{**kwargs, 'start_date': ms, 'end_date': me})
                if df_new is None or df_new.empty:
                    coverage_updates.append({
                        'start_date': ms,
                        'end_date': me,
                        'has_data': False
                    })
                else:
                    df_new['trade_date'] = df_new['trade_date'].astype(str)
                    actual_min = df_new['trade_date'].min()
                    actual_max = df_new['trade_date'].max()
                    coverage_updates.append({
                        'start_date': actual_min,
                        'end_date': actual_max,
                        'has_data': True
                    })
                    # 两侧没有数据的部分要标记为 has_data=False
                    if actual_min > ms:
                        left_end = (datetime.strptime(actual_min, "%Y%m%d") - timedelta(days=1)).strftime("%Y%m%d")
                        if left_end >= ms:
                            coverage_updates.append({
                                'start_date': ms,
                                'end_date': left_end,
                                'has_data': False
                            })
                    if actual_max < me:
                        right_start = (datetime.strptime(actual_max, "%Y%m%d") + timedelta(days=1)).strftime("%Y%m%d")
                        if right_start <= me:
                            coverage_updates.append({
                                'start_date': right_start,
                                'end_date': me,
                                'has_data': False
                            })
                    new_data_frames.append(df_new)

            # 7) 如果有新的数据，就合并进缓存
            if new_data_frames:
                all_new = pd.concat([cached_df] + new_data_frames, ignore_index=True)
                all_new.drop_duplicates(subset=['ts_code', 'trade_date'], inplace=True)
                all_new.sort_values(by=['trade_date'], inplace=True)
                all_new.to_csv(cache_file, index=False)
                final_df = all_new
            else:
                final_df = cached_df

            # 8) 把 coverage_updates 同步到 coverage_file
            if coverage_updates:
                coverage_updates_df = pd.DataFrame(coverage_updates)
                coverage_df = pd.concat([coverage_df, coverage_updates_df], ignore_index=True)
                coverage_df.drop_duplicates(subset=['start_date', 'end_date', 'has_data'], inplace=True)
                coverage_df.to_csv(coverage_file, index=False)

            # 9) 最终返回 [start_date, end_date] 这一段数据
            mask = (final_df['trade_date'] >= str(start_date)) & (final_df['trade_date'] <= str(end_date))
            return final_df.loc[mask].copy()

        return wrapper

    return decorator


class IntegratedFeatureGenerator:
    def __init__(self, cache_dir='cache'):
        ts.set_token(os.environ['TUSHARE'])
        self.pro = ts.pro_api()
        self.cache_dir = cache_dir
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

        self.last_api_call_time = datetime.now() - timedelta(minutes=1)
        self.api_calls_in_last_minute = 0

    def rate_limit(self, max=80):
        now = datetime.now()
        if (now - self.last_api_call_time).total_seconds() >= 60:
            self.api_calls_in_last_minute = 0
            self.last_api_call_time = now
        if self.api_calls_in_last_minute >= max:
            time_to_wait = 60 - (now - self.last_api_call_time).total_seconds()
            time.sleep(time_to_wait)
            self.api_calls_in_last_minute = 0
            self.last_api_call_time = datetime.now()
        self.api_calls_in_last_minute += 1

    @partial_cache_data(key_param_names=('ts_code',))  # 100/s
    def fetch_stock_factors(self, ts_code, start_date, end_date):
        self.rate_limit()
        return self.pro.stk_factor_pro(ts_code=ts_code, start_date=start_date, end_date=end_date,
                                       fields="ts_code,trade_date,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,free_share,total_mv,circ_mv")

    @partial_cache_data(key_param_names=('ts_code',))
    def fetch_stock_basic(self, ts_code):
        return self.pro.stock_basic(ts_code=ts_code, fields='ts_code,industry')

    @partial_cache_data(key_param_names=('ts_code',))
    def fetch_sector_performance(self, ts_code, start_date, end_date):
        if end_date < '20120801':
            return None
        return self.pro.sw_daily(ts_code=ts_code, start_date=start_date, end_date=end_date)

    @partial_cache_data(key_param_names=('ts_code',))
    def fetch_cyq_perf(self, ts_code, start_date, end_date):
        self.rate_limit()
        return self.pro.cyq_perf(ts_code=ts_code, start_date=start_date, end_date=end_date,
                                 fields='ts_code,trade_date,cost_5pct,cost_15pct,cost_50pct,cost_85pct,cost_95pct,weight_avg,winner_rate')

    @partial_cache_data(key_param_names=('ts_code',))
    def fetch_daily_data(self, ts_code, start_date, end_date):
        return self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date,
                              fields='ts_code,trade_date,close')

    @partial_cache_data(key_param_names=('ts_code',))
    def fetch_index_member_all(self, ts_code):
        return self.pro.index_member_all(ts_code=ts_code, fields='l2_code')

    def generate_features(self, stock_code, start_date, end_date):
        factor_df = self.fetch_stock_factors(ts_code=stock_code, start_date=start_date, end_date=end_date)
        if factor_df.empty:
            return pd.DataFrame()

        sector_index_df = self.fetch_index_member_all(ts_code=stock_code)
        sector_performance_df = pd.DataFrame()
        if len(sector_index_df) > 0:
            index_code = sector_index_df['l2_code'].iloc[0]
            sector_performance_df = self.fetch_sector_performance(ts_code=index_code, start_date=start_date,
                                                                  end_date=end_date)

        # cyq_perf_df = self.fetch_cyq_perf(ts_code=stock_code, start_date=start_date, end_date=end_date)
        # daily_data_df = self.fetch_daily_data(ts_code=stock_code, start_date=start_date, end_date=end_date)

        results = []
        for _, row in factor_df.iterrows():
            current_date = row['trade_date']
            result = {
                'date': current_date,
                'sec_id': stock_code[:6],
                'pe': row['pe'],
                'pe_ttm': row['pe_ttm'],
                'pb': row['pb'],
                'ps': row['ps'],
                'ps_ttm': row['ps_ttm'],
                'gx_ratio': row['dv_ratio'],
                'gx_ttm': row['dv_ttm'],
                # 'sector': sector_name
            }

            sector_data = sector_performance_df[sector_performance_df['trade_date'] == current_date]
            if not sector_data.empty:
                result.update({
                    # 'sector_code': sector_data['ts_code'].iloc[0],
                    # 'sector_open': sector_data['open'].iloc[0],
                    # 'sector_close': sector_data['close'].iloc[0],
                    # 'sector_high': sector_data['high'].iloc[0],
                    # 'sector_low': sector_data['low'].iloc[0],
                    # 'sector_vol': sector_data['vol'].iloc[0],
                    # 'sector_amount': sector_data['amount'].iloc[0],
                    'sector_pe': sector_data['pe'].iloc[0],
                    'sector_pb': sector_data['pb'].iloc[0],
                    'sector_pct_change': sector_data['pct_change'].iloc[0]
                })

            # cyq_data = cyq_perf_df[cyq_perf_df['trade_date'] == current_date]
            # daily_close = daily_data_df[daily_data_df['trade_date'] == current_date]
            # if not cyq_data.empty and not daily_close.empty:
            #     close_price = daily_close['close'].iloc[0]
            #     result.update({
            #         'cost_5pct_change': ((cyq_data['cost_5pct'].iloc[0] - close_price) / close_price) * 100,
            #         'cost_15pct_change': ((cyq_data['cost_15pct'].iloc[0] - close_price) / close_price) * 100,
            #         'cost_50pct_change': ((cyq_data['cost_50pct'].iloc[0] - close_price) / close_price) * 100,
            #         'cost_85pct_change': ((cyq_data['cost_85pct'].iloc[0] - close_price) / close_price) * 100,
            #         'cost_95pct_change': ((cyq_data['cost_95pct'].iloc[0] - close_price) / close_price) * 100,
            #         'weight_avg_change': ((cyq_data['weight_avg'].iloc[0] - close_price) / close_price) * 100,
            #         'winner_rate': cyq_data['winner_rate'].iloc[0],
            #         # 'close': close_price
            #     })
            # elif not daily_close.empty:
            #     result['close'] = daily_close['close'].iloc[0]

            results.append(result)

        feature_df = pd.DataFrame(results)
        return feature_df

    def generate_and_save_all(self, index_code, start_date, end_date, output_path='integrated_features.pkl'):
        stock_list_df = D.features(D.instruments(index_code), ['$close'], start_time=start_date,
                                   end_time=end_date).reset_index()
        stock_list_df = stock_list_df.rename(columns={'instrument': 'con_code'})
        stock_codes = stock_list_df['con_code'].unique()

        # Convert stock codes to Tushare format (e.g., 'SH000001' to '000001.SH')
        stock_codes = [code[2:] + '.' + code[:2] for code in stock_codes]

        # Create a list to store all feature DataFrames
        all_features_list = []

        for stock in tqdm(stock_codes, desc="Generating features for all stocks"):
            features = self.generate_features(stock, start_date, end_date)
            if not features.empty:
                all_features_list.append(features)

        # Concatenate all features into a single DataFrame
        if all_features_list:
            combined_features = pd.concat(all_features_list, axis=0)
            # Set multi-index with date and stock_code
            combined_features = combined_features.set_index(['date', 'sec_id'])
            # Sort index
            combined_features = combined_features.sort_index()

            # Save to pickle
            pd.to_pickle(combined_features, output_path)
            print(f"All integrated features saved to {output_path}")
        else:
            print("No features were generated")

    def load_existing_features(self, path):
        if os.path.exists(path):
            return pd.read_pickle(path)
        return pd.DataFrame()

    def merge_with_existing(self, existing_features_path, new_features_path, merged_output_path):
        existing_features = self.load_existing_features(existing_features_path)
        new_features = self.load_existing_features(new_features_path)

        # Both DataFrames should now have multi-index (date, stock_code)
        merged_features = pd.concat([existing_features, new_features])
        # Remove duplicates if any
        merged_features = merged_features[~merged_features.index.duplicated(keep='last')]
        # Sort index
        merged_features = merged_features.sort_index()

        pd.to_pickle(merged_features, merged_output_path)
        print(f"Merged features saved to {merged_output_path}")


def main(index_code='csi300', start_date='20230101', end_date=None, combine=False):
    """
    Generate and save features for stocks in the given index.
    
    Args:
        index_code (str): Index code, e.g., 'csi300' for CSI 300
        start_date (str): Start date in format 'YYYYMMDD'
        end_date (str, optional): End date in format 'YYYYMMDD'. If None, uses current date
    """
    init_qlib()
    feature_generator = IntegratedFeatureGenerator()

    # If end_date is not provided, use current date
    if end_date is None:
        end_date = datetime.today().strftime('%Y%m%d')

    start_date = str(start_date)
    end_date = str(end_date)
    market_code = convert_to_market_code(index_code)
    output_path = f'integrated_features_{market_code}.pkl'
    if not combine:
        feature_generator.generate_and_save_all(index_code, start_date, end_date, output_path)
    else:
        combined_with_integrated(output_path)


def combined_with_integrated(integrated_path):
    a458 = pd.read_pickle(f'refined_a458_{config_info["market"]}.pkl')
    integrated_df = pd.read_pickle(integrated_path)
    integrated_df.index = integrated_df.index.set_levels(pd.to_datetime(integrated_df.index.levels[0]), level=0)
    integrated_df = integrated_df.astype(float)
    df = pd.merge(a458, integrated_df, left_index=True, right_index=True, how='inner')
    df.to_pickle(f'refined_a458i_{config_info["market"]}.pkl')


if __name__ == '__main__':
    fire.Fire(main)
