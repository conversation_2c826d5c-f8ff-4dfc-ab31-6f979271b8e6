import akshare as ak
import pandas as pd
import qlib
from qlib.data import D
from util import notify_by_email_with_attachment


class NewsAnalyzer:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(NewsAnalyzer, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        from transformers import BertForSequenceClassification
        from transformers import BertTokenizer
        from transformers import pipeline
        self.finbert = BertForSequenceClassification.from_pretrained('yiyanghkust/finbert-tone', num_labels=3)
        self.tokenizer = BertTokenizer.from_pretrained('yiyanghkust/finbert-tone')
        self.nlp = pipeline("sentiment-analysis", model=self.finbert, tokenizer=self.tokenizer)
        self.translator = pipeline(
            "translation",
            model="Helsinki-NLP/opus-mt-zh-en",
            tokenizer="Helsinki-NLP/opus-mt-zh-en"
        )

    @staticmethod
    def get_instance():
        if not NewsAnalyzer._instance:
            NewsAnalyzer()
        return NewsAnalyzer._instance

    def translate(self, text):
        try:
            return self.translator(text, max_length=2048)[0]["translation_text"]
        except Exception as e:
            print(e)
            return text

    def get_news_score(self, stock_code, start_date=None):
        news = ak.stock_news_em(symbol=stock_code)
        positive, neutral, negative, pc, nlc, neg, score_p, score_n = 0, 0, 0, 0, 0, 0, 0, 0
        if len(news) > 0:
            if start_date is not None:
                news = news.loc[news["发布时间"] > start_date, :]
            trans_contents = (news['新闻标题'].apply(lambda x: self.translate(x))).tolist()
            scores = self.nlp(trans_contents)
            for score in scores:
                if score['label'] == 'Positive':
                    positive += score['score']
                    pc += 1
                elif score['label'] == 'Neutral':
                    neutral += score['score']
                    nlc += 1
                else:
                    negative += score['score']
                    neg += 1
            score_p = positive / (negative + 0.01)
            score_n = positive / (neutral + 0.01)
        return pd.DataFrame([[positive, neutral, negative, pc, nlc, neg, score_p, score_n]],
                            columns=['Positive', 'Neutral', 'Negative', 'Positive Count', 'Neutral Count',
                                     'Negative Count', 'Positive Score', 'Neutral Score'], index=[stock_code])


if __name__ == '__main__':
    date = pd.to_datetime('today') - pd.Timedelta(days=1)
    date = date.strftime('%Y-%m-%d')
    qlib.init(provider_uri='./qlib_bin', region='cn')
    res = pd.DataFrame()
    instruments = D.list_instruments(D.instruments(market='csi300'), start_time='2022-12-31')
    for ins in instruments:
        code = ins[2:]
        try:
            line = NewsAnalyzer.get_instance().get_news_score(code, date)
            print(line)
            res = pd.concat([res, line])
        except Exception as e:
            print(e)
            continue
    res.to_csv('news_score.csv')
    notify_by_email_with_attachment('news_score.csv', 'news_score.csv')
